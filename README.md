# 🧠 CrewAI OSINT Agent Framework

A sophisticated multi-agent **Open Source Intelligence (OSINT)** framework built with **CrewAI**, **LangChain**, **LlamaIndex**, and **DSPy** for advanced intelligence gathering, analysis, and automation.

## 🎯 Overview

This enterprise-grade framework provides specialized AI agents for comprehensive intelligence operations:
- **🌍 Geopolitical Intelligence Analysis** - International relations, conflicts, and political developments
- **🔒 Cyber Threat Intelligence (CTI)** - Threat actor tracking, IOC extraction, and security analysis
- **🖼️ Multi-Modal OSINT** - Image/video analysis with forensic capabilities
- **🌐 Browser Automation** - Stealth web investigation and social media monitoring
- **💬 Stateful Conversations** - Memory-persistent agents with context awareness

### 🌟 Key Features

#### Core Intelligence Capabilities
- 🤖 **Multi-Agent Architecture** - Specialized agents with distinct capabilities and memory
- 🔍 **Advanced RAG Pipeline** - Vector-based document indexing and intelligent retrieval
- 🌐 **Web Intelligence Gathering** - Automated web search and content extraction
- 📊 **Prompt Optimization** - DSPy-powered evaluation and improvement
- 🔧 **Modular Tool System** - Extensible tool wrappers for external services
- 📈 **Comprehensive Workflows** - Event-driven, parallel processing pipelines

#### Advanced Features
- 🖼️ **Multi-Modal Analysis** - OCR, face recognition, object detection, forensic analysis
- 🌐 **Browser Automation** - Selenium, Playwright, undetected Chrome for stealth operations
- 🧠 **Memory Management** - Persistent conversation history and context with LangGraph
- 🔄 **Workflow Engine** - Event-driven workflows with conditional logic and scheduling
- 🔒 **Security Suite** - Data encryption, user authentication, privacy protection
- 📊 **Real-time Monitoring** - Health checks, metrics, alerting, and performance tracking

#### Enterprise Features
- 🚀 **RESTful API** - Complete API interface with OpenAPI documentation
- 🐳 **Docker Containerization** - Easy deployment and scaling
- 🔐 **Authentication & Authorization** - JWT-based session management and RBAC
- 📝 **Comprehensive Logging** - Structured logging with multiple outputs
- 🧪 **Automated Testing** - Continuous evaluation and quality assurance pipeline
- 🎨 **Interactive UI** - Streamlit-based web interface for easy interaction

## 🏗️ System Architecture

### 🏛️ System Architecture Diagram

The following diagram illustrates the complete system architecture, showing how all components interact to provide comprehensive OSINT capabilities:

```mermaid
graph TB
    %% User Interface Layer
    subgraph "🎨 User Interface Layer"
        UI[🎨 Streamlit UI<br/>Interactive Web Interface]
        API[🚀 FastAPI Server<br/>RESTful API Endpoints]
        CLI[💻 CLI Tools<br/>Command Line Interface]
    end

    %% Authentication & Security Layer
    subgraph "🔒 Security & Authentication Layer"
        AUTH[🛡️ Authentication Manager<br/>JWT-based Sessions<br/>User Management<br/>RBAC Permissions]
        ENCRYPT[🔐 Encryption Manager<br/>AES Data Encryption<br/>Secure Key Management]
        PRIVACY[🔍 Privacy Manager<br/>Sensitive Data Detection<br/>Text Sanitization<br/>Privacy Reports]
        LOCAL_EMB[🏠 Local Embeddings<br/>Privacy-preserving<br/>Text Analysis]
    end

    %% Agent Layer
    subgraph "🤖 Intelligent Agent Layer"
        subgraph "Standard Agents"
            GEO[🌍 Geopolitical Agent<br/>• Regional Analysis<br/>• Intelligence Briefs<br/>• Political Monitoring<br/>• Diplomatic Relations]
            CTI[🔒 CTI Agent<br/>• Threat Analysis<br/>• IOC Extraction<br/>• Actor Tracking<br/>• Campaign Correlation]
            MULTI[🖼️ Multi-Modal Agent<br/>• Image Analysis<br/>• Video Processing<br/>• OCR & Face Recognition<br/>• Forensic Analysis]
            BROWSER[🌐 Browser Agent<br/>• Stealth Investigation<br/>• Social Media Monitoring<br/>• Dynamic Content Extraction<br/>• Automated Interaction]
        end

        subgraph "Stateful Agents"
            SGEO[🧠 Stateful Geo Agent<br/>• Memory Persistence<br/>• Context Awareness<br/>• Conversation History]
            SCTI[🧠 Stateful CTI Agent<br/>• Memory Persistence<br/>• Context Awareness<br/>• Conversation History]
        end

        BASE[⚙️ Base Agent<br/>Common Functionality<br/>CrewAI Integration<br/>DSPy Optimization]
    end

    %% Tool Layer
    subgraph "🔧 Advanced Tool Layer"
        subgraph "Web Intelligence"
            SERPER[🔍 Serper Search<br/>• Google Search API<br/>• News Search<br/>• Image Search<br/>• Geographic Filtering<br/>• Time-based Filtering]
            CRAWL[🕷️ Crawl4AI<br/>• Structured Extraction<br/>• Keyword Focusing<br/>• Link Analysis<br/>• Content Credibility<br/>• Metadata Extraction]
        end

        subgraph "Multi-Modal Tools"
            MM_ANALYZER[🖼️ Multi-Modal Analyzer<br/>• OCR Processing<br/>• Face Recognition<br/>• Object Detection<br/>• Metadata Extraction<br/>• Forensic Analysis]
            BROWSER_AUTO[🌐 Browser Automation<br/>• Selenium WebDriver<br/>• Playwright Integration<br/>• Undetected Chrome<br/>• Proxy Support<br/>• Anti-Detection]
        end

        subgraph "Knowledge Management"
            RAG_TOOLS[📚 LlamaIndex RAG<br/>• Vector Search<br/>• Document Indexing<br/>• Semantic Retrieval<br/>• Hybrid Search<br/>• Knowledge Base]
        end
    end

    %% Workflow Engine
    subgraph "🔄 Workflow Orchestration Engine"
        WF_ENGINE[⚙️ Workflow Engine<br/>• Event-driven Execution<br/>• Parallel Processing<br/>• Conditional Logic<br/>• Error Handling<br/>• State Management]
        WF_BUILDER[🏗️ Workflow Builder<br/>• Fluent Interface<br/>• Task Dependencies<br/>• Template System<br/>• Validation]
        SCHEDULER[⏰ Scheduler<br/>• Cron-based Scheduling<br/>• Event Triggers<br/>• Webhook Integration<br/>• File Monitoring]

        subgraph "Pre-built Workflows"
            GEO_WF[🌍 Geo Workflow<br/>• Regional Analysis<br/>• Situation Reports<br/>• Multi-region Monitoring]
            CTI_WF[🔒 CTI Workflow<br/>• Threat Intelligence<br/>• IOC Processing<br/>• Campaign Analysis]
            EVAL_WF[🧪 DSPy Evaluator<br/>• Performance Evaluation<br/>• Prompt Optimization<br/>• Quality Metrics]
        end
    end

    %% Memory & State Management
    subgraph "🧠 Memory & State Management"
        MEMORY[💾 Memory Manager<br/>• LangGraph Integration<br/>• Conversation Persistence<br/>• Context Management<br/>• State Tracking]
        CONV_DB[(🗄️ Conversation DB<br/>SQLite Database<br/>Session Storage<br/>User Context)]
    end

    %% RAG & Knowledge Base
    subgraph "📚 Knowledge & Retrieval System"
        INDEX_BUILDER[🏗️ Index Builder<br/>• Document Processing<br/>• Vector Indexing<br/>• Batch Processing<br/>• Deduplication<br/>• Metadata Enrichment]
        RETRIEVER[🔍 Document Retriever<br/>• Semantic Search<br/>• Keyword Search<br/>• Hybrid Retrieval<br/>• Result Ranking<br/>• Filtering]
        VECTOR_DB[(🗃️ Vector Database<br/>Embeddings Storage<br/>Document Index<br/>Metadata Store)]
    end

    %% Monitoring & Observability
    subgraph "📊 Monitoring & Observability"
        HEALTH[❤️ Health Checker<br/>• System Health<br/>• Component Status<br/>• Dependency Checks<br/>• Performance Metrics]
        METRICS[📈 Metrics Collector<br/>• Performance Tracking<br/>• Usage Statistics<br/>• Resource Monitoring<br/>• Custom Metrics]
        ALERTS[🚨 Alert Manager<br/>• Rule-based Alerting<br/>• Notification System<br/>• Escalation Policies<br/>• Alert History]
        LOGGING[📝 Logging System<br/>• Structured Logging<br/>• Multiple Outputs<br/>• Log Aggregation<br/>• Error Tracking]
    end

    %% Data Storage
    subgraph "💾 Data Storage Layer"
        AUTH_DB[(🔐 Auth Database<br/>User Accounts<br/>Sessions<br/>Permissions)]
        FILE_STORAGE[(📁 File Storage<br/>Uploaded Files<br/>Analysis Results<br/>Reports)]
        CACHE[(⚡ Cache Layer<br/>Redis Cache<br/>Query Results<br/>Session Data)]
    end

    %% External Services
    subgraph "🌐 External Services"
        OPENAI[🤖 OpenAI API<br/>GPT Models<br/>Embeddings<br/>Completions]
        SERPER_API[🔍 Serper API<br/>Google Search<br/>News Search<br/>Image Search]
        WEB[🌍 Web Sources<br/>Target Websites<br/>Social Media<br/>News Sources]
    end

    %% Connections - User Interface
    UI --> API
    CLI --> API
    API --> AUTH

    %% Connections - Security
    AUTH --> ENCRYPT
    AUTH --> PRIVACY
    PRIVACY --> LOCAL_EMB

    %% Connections - Agents
    API --> GEO
    API --> CTI
    API --> MULTI
    API --> BROWSER
    API --> SGEO
    API --> SCTI

    GEO --> BASE
    CTI --> BASE
    MULTI --> BASE
    BROWSER --> BASE
    SGEO --> BASE
    SCTI --> BASE

    %% Connections - Stateful Agents to Memory
    SGEO --> MEMORY
    SCTI --> MEMORY
    MEMORY --> CONV_DB

    %% Connections - Agents to Tools
    GEO --> SERPER
    GEO --> CRAWL
    GEO --> RAG_TOOLS

    CTI --> SERPER
    CTI --> CRAWL
    CTI --> RAG_TOOLS

    MULTI --> MM_ANALYZER
    MULTI --> SERPER
    MULTI --> CRAWL

    BROWSER --> BROWSER_AUTO
    BROWSER --> SERPER
    BROWSER --> CRAWL

    %% Connections - Tools to External Services
    SERPER --> SERPER_API
    CRAWL --> WEB
    MM_ANALYZER --> FILE_STORAGE
    BROWSER_AUTO --> WEB
    RAG_TOOLS --> VECTOR_DB

    %% Connections - Workflow Engine
    API --> WF_ENGINE
    WF_ENGINE --> WF_BUILDER
    WF_ENGINE --> SCHEDULER
    WF_ENGINE --> GEO_WF
    WF_ENGINE --> CTI_WF
    WF_ENGINE --> EVAL_WF

    WF_ENGINE --> GEO
    WF_ENGINE --> CTI
    WF_ENGINE --> MULTI
    WF_ENGINE --> BROWSER

    %% Connections - RAG System
    RAG_TOOLS --> INDEX_BUILDER
    RAG_TOOLS --> RETRIEVER
    INDEX_BUILDER --> VECTOR_DB
    RETRIEVER --> VECTOR_DB

    %% Connections - Monitoring
    API --> HEALTH
    API --> METRICS
    API --> ALERTS
    API --> LOGGING

    HEALTH --> METRICS
    METRICS --> ALERTS
    ALERTS --> LOGGING

    %% Connections - Storage
    AUTH --> AUTH_DB
    API --> FILE_STORAGE
    API --> CACHE
    METRICS --> CACHE

    %% Connections - External Services
    BASE --> OPENAI
    LOCAL_EMB --> OPENAI

    %% Styling
    classDef agentClass fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef toolClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef workflowClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef securityClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef storageClass fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef externalClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef monitoringClass fill:#e0f2f1,stroke:#004d40,stroke-width:2px

    class GEO,CTI,MULTI,BROWSER,SGEO,SCTI,BASE agentClass
    class SERPER,CRAWL,MM_ANALYZER,BROWSER_AUTO,RAG_TOOLS toolClass
    class WF_ENGINE,WF_BUILDER,SCHEDULER,GEO_WF,CTI_WF,EVAL_WF workflowClass
    class AUTH,ENCRYPT,PRIVACY,LOCAL_EMB securityClass
    class AUTH_DB,CONV_DB,VECTOR_DB,FILE_STORAGE,CACHE storageClass
    class OPENAI,SERPER_API,WEB externalClass
    class HEALTH,METRICS,ALERTS,LOGGING,MEMORY,INDEX_BUILDER,RETRIEVER monitoringClass
```

### 📁 Project Structure

```
crewai-osint-framework/
├── agents/                          # 🤖 Specialized OSINT agents
│   ├── base_agent.py               # Base agent class with common functionality
│   ├── geo_agent.py                # Geopolitical intelligence analyst
│   ├── cti_agent.py                # Cyber threat intelligence analyst
│   ├── multimodal_osint_agent.py   # Multi-modal analysis agent
│   ├── browser_osint_agent.py      # Browser automation agent
│   ├── stateful_geo_agent.py       # Memory-persistent geopolitical agent
│   └── stateful_cti_agent.py       # Memory-persistent CTI agent
├── tools/                          # 🔧 External service integrations
│   ├── serper_wrapper.py           # Google Search API wrapper
│   ├── crawl4ai_wrapper.py         # Web crawling and extraction
│   ├── llamaindex_tools.py         # RAG and document processing
│   ├── multimodal_analyzer.py      # Image/video analysis tools
│   └── browser_automation.py       # Browser automation utilities
├── workflows/                      # 🔄 Analysis orchestration
│   ├── workflow_engine.py          # Event-driven workflow execution
│   ├── workflow_builder.py         # Fluent workflow construction
│   ├── scheduler.py                # Cron-based task scheduling
│   ├── langchain_geo_workflow.py   # Geopolitical analysis pipeline
│   ├── llamaindex_cti_workflow.py  # CTI analysis pipeline
│   └── dspy_evaluator.py           # Performance evaluation framework
├── rag/                           # 📚 Document processing and retrieval
│   ├── index_builder.py           # Vector index creation and management
│   └── retriever.py               # Advanced semantic and keyword search
├── api/                           # 🚀 RESTful API interface
│   ├── main.py                    # FastAPI application with all endpoints
│   └── README.md                  # API documentation
├── ui/                            # 🎨 User interfaces
│   ├── streamlit_app.py           # Interactive web interface
│   └── README.md                  # UI documentation
├── utils/                         # 🛠️ Core utilities and services
│   ├── security.py                # Authentication, encryption, privacy
│   ├── monitoring.py              # Health checks, metrics, alerting
│   ├── memory_manager.py          # Conversation state management
│   ├── logging_config.py          # Structured logging configuration
│   ├── error_handling.py          # Error handling and recovery
│   └── config_validator.py        # Configuration validation
├── data/                          # 💾 Persistent storage
│   ├── vector_index/              # Vector embeddings and indices
│   ├── memory.db                  # Conversation memory database
│   ├── auth.db                    # User authentication database
│   └── crawled_docs/              # Cached web content
├── docker/                        # 🐳 Containerization
│   ├── docker-compose.yml         # Multi-service deployment
│   ├── Dockerfile                 # Production container
│   └── deploy.sh                  # Deployment automation
├── tests/                         # 🧪 Testing framework
│   ├── unit/                      # Unit tests
│   ├── integration/               # Integration tests
│   └── e2e/                       # End-to-end tests
├── examples/                      # 📖 Usage demonstrations
├── docs/                          # 📚 Comprehensive documentation
└── output/                        # 📊 Generated reports and analysis
```

### 🔍 Architecture Component Details

#### 🎨 User Interface Layer
- **Streamlit UI**: Interactive web interface for easy agent interaction and result visualization
- **FastAPI Server**: High-performance RESTful API with automatic OpenAPI documentation
- **CLI Tools**: Command-line interface for automation and scripting

#### 🔒 Security & Authentication Layer
- **Authentication Manager**: JWT-based session management with role-based access control (RBAC)
- **Encryption Manager**: AES encryption for sensitive data with secure key management
- **Privacy Manager**: Sensitive data detection, text sanitization, and privacy compliance
- **Local Embeddings**: Privacy-preserving text analysis without external API dependencies

#### 🤖 Intelligent Agent Layer
- **Base Agent**: Common functionality shared across all agents (CrewAI integration, DSPy optimization)
- **Geopolitical Agent**: Specialized for international relations, regional analysis, and political intelligence
- **CTI Agent**: Focused on cyber threat intelligence, IOC extraction, and threat actor tracking
- **Multi-Modal Agent**: Advanced image/video analysis with forensic capabilities
- **Browser Agent**: Stealth web investigation and social media monitoring
- **Stateful Agents**: Memory-persistent versions with conversation context and history

#### 🔧 Advanced Tool Layer
- **Serper Search**: Google Search API integration with advanced filtering and targeting
- **Crawl4AI**: Intelligent web crawling with structured content extraction
- **Multi-Modal Analyzer**: Comprehensive image/video processing with OCR, face recognition, and forensics
- **Browser Automation**: Multi-browser support with anti-detection and stealth capabilities
- **LlamaIndex RAG**: Advanced document indexing and semantic retrieval system

#### 🔄 Workflow Orchestration Engine
- **Workflow Engine**: Event-driven execution with parallel processing and conditional logic
- **Workflow Builder**: Fluent interface for creating complex multi-agent workflows
- **Scheduler**: Cron-based scheduling with event triggers and webhook integration
- **Pre-built Workflows**: Ready-to-use templates for common OSINT operations

#### 🧠 Memory & State Management
- **Memory Manager**: LangGraph integration for conversation persistence and context management
- **Conversation Database**: SQLite storage for session data and user context

#### 📚 Knowledge & Retrieval System
- **Index Builder**: Document processing with vector indexing and metadata enrichment
- **Document Retriever**: Advanced semantic and keyword search with result ranking
- **Vector Database**: Embeddings storage with efficient similarity search

#### 📊 Monitoring & Observability
- **Health Checker**: System health monitoring with component status tracking
- **Metrics Collector**: Performance tracking and resource monitoring
- **Alert Manager**: Rule-based alerting with notification and escalation
- **Logging System**: Structured logging with multiple outputs and error tracking

#### 💾 Data Storage Layer
- **Authentication Database**: User accounts, sessions, and permissions
- **File Storage**: Uploaded files, analysis results, and generated reports
- **Cache Layer**: Redis caching for improved performance

#### 🌐 External Services
- **OpenAI API**: GPT models for language processing and embeddings
- **Serper API**: Google Search integration for web intelligence
- **Web Sources**: Target websites, social media platforms, and news sources

## 🚀 Quick Start

### 1. Prerequisites

- **Python 3.9+** (3.10+ recommended)
- **Docker** (optional, for containerized deployment)
- **System Dependencies** (for multi-modal analysis):
  ```bash
  # Ubuntu/Debian
  sudo apt-get install tesseract-ocr tesseract-ocr-eng libgl1-mesa-glx

  # macOS
  brew install tesseract

  # Install Playwright browsers
  playwright install chromium firefox
  ```

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd crewai-osint-framework

# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Validate installation
python validate_setup.py
```

### 3. Configuration

Create a `.env` file with your API keys:

```bash
# Required API Keys
OPENAI_API_KEY=your_openai_api_key_here
SERPER_API_KEY=your_serper_api_key_here

# Optional LLM Providers
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Application Settings
LOG_LEVEL=INFO
ENVIRONMENT=development
API_HOST=0.0.0.0
API_PORT=8000

# Security Settings (generate secure keys)
OSINT_MASTER_KEY=your_32_character_encryption_key
OSINT_SECRET_KEY=your_jwt_secret_key

# Memory and Storage
MEMORY_BACKEND=langgraph
MEMORY_PATH=./data/memory

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
```

### 4. Launch Options

#### Option A: API Server
```bash
# Start the FastAPI server
python -m uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload

# Access API documentation at http://localhost:8000/docs
```

#### Option B: Interactive UI
```bash
# Launch Streamlit interface
streamlit run ui/streamlit_app.py

# Access UI at http://localhost:8501
```

#### Option C: Docker Deployment
```bash
# Quick start with Docker Compose
docker-compose up -d

# Access services:
# - API: http://localhost:8000
# - UI: http://localhost:8501
# - Metrics: http://localhost:9090
```

### 5. Quick Test

```bash
# Run basic functionality test
python test_basic.py

# Run comprehensive demo
python examples/simple_demo.py

# Test specific capabilities
python examples/geopolitical_analysis_example.py
python examples/cti_analysis_example.py
python examples/multimodal_analysis_example.py
```

## 📋 Core Components & Capabilities

### 🤖 Specialized Agents

#### 🌍 Geopolitical Intelligence Agent
Advanced international relations and political analysis:

```python
from agents.geo_agent import GeopoliticalAgent
from agents.stateful_geo_agent import StatefulGeopoliticalAgent

# Standard geopolitical agent
geo_agent = GeopoliticalAgent()

# Perform regional analysis
result = geo_agent.analyze(
    query="Recent diplomatic tensions in Eastern Europe",
    time_range="7d",
    regions=["Eastern Europe"]
)

# Generate intelligence brief
brief = geo_agent.generate_intelligence_brief(
    topic="NATO expansion implications",
    classification="UNCLASSIFIED"
)

# Stateful agent with memory
stateful_geo = StatefulGeopoliticalAgent(user_id="analyst_001")
conversation_id = stateful_geo.start_conversation()

# Context-aware analysis
contextual_result = stateful_geo.analyze_with_context(
    query="Follow-up on previous Eastern Europe analysis",
    analysis_type="intelligence_brief"
)
```

#### 🔒 Cyber Threat Intelligence Agent
Comprehensive cybersecurity threat analysis:

```python
from agents.cti_agent import CTIAgent
from agents.stateful_cti_agent import StatefulCTIAgent

# Standard CTI agent
cti_agent = CTIAgent()

# Analyze threats
analysis = cti_agent.analyze(
    query="Recent APT28 activities",
    threat_type="apt"
)

# Extract IOCs with advanced patterns
iocs = cti_agent.extract_iocs(threat_report_text)

# Track threat actors
profile = cti_agent.track_threat_actor("Lazarus")

# Stateful CTI agent
stateful_cti = StatefulCTIAgent(user_id="security_analyst")
threat_analysis = stateful_cti.analyze_threat_with_context(
    content="Suspicious network activity detected",
    analysis_type="ioc_extraction"
)
```

#### 🖼️ Multi-Modal OSINT Agent
Advanced image and video analysis with OSINT context:

```python
from agents.multimodal_osint_agent import MultiModalOSINTAgent

# Initialize multi-modal agent
multimodal_agent = MultiModalOSINTAgent(user_id="investigator")

# Comprehensive media analysis
result = multimodal_agent.analyze_media_with_context(
    media_input="path/to/suspicious_image.jpg",
    media_type="image",
    analysis_types=["metadata", "ocr", "faces", "objects", "forensics"],
    osint_context=True
)

# Analyze video content
video_result = multimodal_agent.analyze_media_with_context(
    media_input="path/to/video.mp4",
    media_type="video",
    analysis_types=["metadata", "frames", "audio"],
    osint_context=True
)
```

#### 🌐 Browser OSINT Agent
Stealth web investigation and automation:

```python
from agents.browser_osint_agent import BrowserOSINTAgent

# Initialize with stealth capabilities
browser_agent = BrowserOSINTAgent(
    user_id="web_investigator",
    headless=True,
    stealth=True
)

# Comprehensive target investigation
investigation = browser_agent.investigate_target(
    target="suspicious-domain.com",
    investigation_type="comprehensive"
)

# Social media investigation
social_result = browser_agent.investigate_target(
    target="@suspicious_account",
    investigation_type="social_media"
)

# Advanced web scraping
scraping_result = browser_agent.investigate_target(
    target="https://target-site.com",
    investigation_type="advanced_scraping"
)
```

### 🔄 Advanced Workflow System

#### Event-Driven Workflow Engine
```python
from workflows.workflow_builder import WorkflowBuilder, WorkflowTemplates
from workflows.workflow_engine import workflow_engine
from workflows.scheduler import workflow_scheduler

# Build custom workflow
builder = WorkflowBuilder("threat_monitoring", "Continuous threat monitoring")
builder.add_agent_task(
    "search_threats",
    agent_type="cti",
    parameters={"query": "latest APT activities", "threat_type": "apt"}
).add_agent_task(
    "analyze_findings",
    agent_type="multimodal",
    parameters={"analysis_types": ["metadata", "forensics"]},
    depends_on=["search_threats"]
).add_webhook_task(
    "notify_team",
    webhook_url="https://your-team-webhook.com",
    depends_on=["analyze_findings"]
)

# Register and execute workflow
workflow_id = builder.register()
execution_id = await workflow_engine.execute_workflow(
    workflow_id=workflow_id,
    initial_context={"priority": "high"}
)

# Schedule recurring execution
schedule_id = workflow_scheduler.schedule_workflow(
    workflow_id=workflow_id,
    trigger_type="cron",
    schedule="0 */6 * * *",  # Every 6 hours
    context={"automated": True}
)
```

#### Pre-built Workflow Templates
```python
# Comprehensive target investigation
investigation_workflow = WorkflowTemplates.comprehensive_target_investigation(
    target="suspicious-domain.com"
)

# Threat monitoring pipeline
monitoring_workflow = WorkflowTemplates.threat_monitoring_pipeline(
    keywords=["APT", "ransomware", "zero-day"],
    update_frequency="6h"
)

# Social media monitoring
social_workflow = WorkflowTemplates.social_media_monitoring(
    platforms=["twitter", "telegram"],
    keywords=["cyber attack", "data breach"]
)

# Batch domain analysis
batch_workflow = WorkflowTemplates.batch_domain_analysis(
    domains=["domain1.com", "domain2.com", "domain3.com"]
)
```

#### Traditional Workflows
```python
from workflows.langchain_geo_workflow import GeopoliticalWorkflow
from workflows.llamaindex_cti_workflow import CTIWorkflow

# Geopolitical analysis workflow
geo_workflow = GeopoliticalWorkflow()
result = geo_workflow.analyze_geopolitical_situation(
    topic="Regional stability assessment",
    regions=["Middle East", "Eastern Europe"],
    time_range="30d"
)

# CTI workflow with RAG
cti_workflow = CTIWorkflow()
cti_workflow.ingest_threat_reports(
    reports=threat_reports,
    urls=threat_intel_urls
)

analysis = cti_workflow.analyze_threat(
    threat_query="Advanced persistent threats targeting finance",
    include_historical=True
)
```

### 🧪 Performance Evaluation & Optimization

```python
from workflows.dspy_evaluator import DSPyEvaluator

evaluator = DSPyEvaluator()

# Create evaluation dataset
dataset = evaluator.create_evaluation_dataset(
    domain="geopolitical",
    examples=evaluation_examples
)

# Evaluate analysis quality
evaluation = evaluator.evaluate_analysis_quality(
    domain="geopolitical",
    query="Analysis query",
    analysis="Generated analysis"
)

# Optimize prompts
optimized_module, results = evaluator.optimize_prompts(
    domain="geopolitical",
    module_to_optimize=analysis_module
)
```

## 🛠️ Advanced Tools & Integrations

### 🔍 Web Intelligence Tools

#### Serper.dev Search Integration
- **Google Search API** - Comprehensive web search with advanced filtering
- **News Search** - Real-time news monitoring and analysis
- **Image Search** - Visual content discovery and analysis
- **Time-based Filtering** - Historical and recent content targeting
- **Geographic Targeting** - Location-specific intelligence gathering
- **Multiple Search Types** - Web, news, images, videos, places, shopping

#### Crawl4AI Web Extraction
- **Structured Content Extraction** - Intelligent content parsing and analysis
- **Keyword-focused Crawling** - Targeted information extraction
- **News Article Processing** - Automated journalism content analysis
- **Metadata Extraction** - Comprehensive document metadata collection
- **Link Analysis** - Internal/external link categorization
- **Content Credibility Assessment** - Source reliability evaluation

### 🖼️ Multi-Modal Analysis Tools

#### Image & Video Processing
- **OCR (Optical Character Recognition)** - Text extraction from images
- **Face Recognition & Analysis** - Identity detection and demographic analysis
- **Object Detection** - Scene understanding and content identification
- **Metadata Extraction** - EXIF data and technical information
- **Forensic Analysis** - Image authenticity and manipulation detection
- **Geolocation Analysis** - Location identification from visual cues

#### Advanced Capabilities
- **Video Frame Analysis** - Key frame extraction and analysis
- **Audio Processing** - Speech-to-text and audio fingerprinting
- **Document Analysis** - PDF, document, and text processing
- **Social Media Content** - Platform-specific content extraction

### 🌐 Browser Automation Suite

#### Stealth Web Operations
- **Undetected Chrome** - Anti-detection browser automation
- **Selenium WebDriver** - Standard web automation capabilities
- **Playwright Integration** - Modern browser automation with advanced features
- **Proxy Support** - IP rotation and anonymization
- **User Agent Rotation** - Browser fingerprint management

#### Investigation Capabilities
- **Social Media Monitoring** - Platform-specific data collection
- **Dynamic Content Extraction** - JavaScript-rendered content analysis
- **Form Automation** - Automated interaction with web forms
- **Screenshot Capture** - Visual documentation of web content
- **Network Traffic Analysis** - Request/response monitoring

### 📚 Knowledge Management (RAG)

#### Vector Search & Retrieval
- **LlamaIndex Integration** - Advanced document indexing and retrieval
- **Semantic Similarity Search** - Context-aware document matching
- **Hybrid Search** - Combined keyword and semantic search
- **Persistent Storage** - Long-term knowledge base management
- **Incremental Updates** - Real-time knowledge base updates
- **Deduplication** - Intelligent content deduplication

#### Document Processing
- **Multi-format Support** - PDF, Word, text, web content
- **Batch Processing** - Large-scale document ingestion
- **Metadata Enrichment** - Automatic tagging and categorization
- **Content Summarization** - Intelligent document summarization

## 🚀 RESTful API Interface

### 📡 Core API Endpoints

#### Authentication & Session Management
```bash
# User authentication
POST /api/v1/auth/login
POST /api/v1/auth/logout
GET /api/v1/auth/validate

# User management
POST /api/v1/users/create
GET /api/v1/users/profile
```

#### Agent Analysis Endpoints
```bash
# Geopolitical analysis
POST /api/v1/geopolitical/analyze
POST /api/v1/geopolitical/intelligence-brief
POST /api/v1/geopolitical/situation-report

# CTI analysis
POST /api/v1/cti/analyze
POST /api/v1/cti/extract-iocs
POST /api/v1/cti/track-actor

# Multi-modal analysis
POST /api/v1/multimodal/analyze-upload
POST /api/v1/multimodal/analyze-url

# Browser investigation
POST /api/v1/browser/investigate
POST /api/v1/browser/scrape-advanced
```

#### Workflow Management
```bash
# Workflow execution
POST /api/v1/workflows/execute
GET /api/v1/workflows/{execution_id}/status
POST /api/v1/workflows/{execution_id}/cancel

# Workflow scheduling
POST /api/v1/workflows/schedule
GET /api/v1/workflows/scheduled
DELETE /api/v1/workflows/scheduled/{schedule_id}
```

#### System Monitoring
```bash
# Health checks
GET /health
GET /health/detailed
GET /metrics

# System status
GET /api/v1/system/status
GET /api/v1/system/metrics
```

### 📊 Comprehensive Use Cases

#### 🌍 Geopolitical Intelligence Operations
- **Diplomatic Relations Analysis** - Track bilateral and multilateral relationships
- **Conflict Monitoring** - Real-time assessment of regional tensions and security situations
- **Policy Impact Assessment** - Analyze effects of political decisions and policy changes
- **Election Analysis** - Monitor political developments and electoral implications
- **Economic Intelligence** - Trade relationships and economic policy analysis
- **Regional Stability Assessment** - Multi-factor stability and risk analysis

#### 🔒 Cyber Threat Intelligence Operations
- **APT Campaign Tracking** - Monitor advanced persistent threat groups and campaigns
- **IOC Extraction & Validation** - Automatically extract and validate indicators of compromise
- **Malware Family Analysis** - Analyze malware families, variants, and evolution
- **Vulnerability Intelligence** - Track exploitation patterns and vulnerability trends
- **Dark Web Monitoring** - Monitor underground forums and marketplaces
- **Threat Actor Profiling** - Comprehensive threat actor attribution and profiling

#### 🖼️ Multi-Modal OSINT Operations
- **Image Forensics** - Detect manipulated or synthetic media content
- **Geolocation Analysis** - Identify locations from visual and metadata clues
- **Person Identification** - Face recognition and demographic analysis
- **Document Analysis** - Extract and analyze text from images and documents
- **Social Media Investigation** - Analyze visual content from social platforms
- **Evidence Collection** - Forensic-grade evidence collection and documentation

#### 🌐 Web Investigation Operations
- **Domain Investigation** - Comprehensive domain and infrastructure analysis
- **Social Media Monitoring** - Multi-platform social media intelligence gathering
- **Website Analysis** - Technical and content analysis of web properties
- **Data Breach Investigation** - Investigate and analyze data breach incidents
- **Phishing Campaign Analysis** - Identify and analyze phishing operations
- **Brand Protection** - Monitor for brand abuse and impersonation

#### 🔄 Cross-Domain Analysis
- **Geopolitical Cyber Nexus** - Analyze state-sponsored cyber activities and attribution
- **Economic Security Assessment** - Assess cyber threats to critical infrastructure
- **Information Operations** - Track disinformation campaigns and influence operations
- **Hybrid Threat Analysis** - Multi-domain threat analysis and correlation
- **Supply Chain Security** - Analyze supply chain risks and vulnerabilities

## 🔒 Security & Privacy Features

### 🛡️ Authentication & Authorization
```python
from utils.security import auth_manager

# User management
user_id = auth_manager.create_user(
    username="analyst",
    password="secure_password",
    permissions=["read", "write", "admin"]
)

# Session management
session_id = auth_manager.authenticate_user(
    username="analyst",
    password="secure_password",
    ip_address="*************"
)

# Permission validation
has_access = auth_manager.validate_session_permissions(
    session_id, required_permissions=["write"]
)
```

### 🔐 Data Encryption & Privacy
```python
from utils.security import encryption_manager, privacy_manager

# Encrypt sensitive data
encrypted_data = encryption_manager.encrypt_data("sensitive_information")

# Privacy-preserving analysis
privacy_report = privacy_manager.scan_for_sensitive_data(text_content)
sanitized_text = privacy_manager.sanitize_text(text_content)

# Local embeddings for privacy
from utils.security import local_embeddings
embeddings = local_embeddings.generate_embeddings(documents)
```

### 📊 Monitoring & Alerting
```python
from utils.monitoring import health_checker, metrics_collector, alert_manager

# System health monitoring
health_status = health_checker.get_system_health()

# Performance metrics
metrics = metrics_collector.get_current_metrics()

# Custom alerts
alert_manager.add_alert_rule(
    name="high_cpu_usage",
    condition=lambda m: m["system"]["cpu_percent"] > 80,
    severity="warning",
    cooldown_minutes=5
)
```

## 🔧 Advanced Configuration

### 🤖 Agent Configuration
```python
# Custom agent initialization with advanced settings
agent = GeopoliticalAgent(
    llm_model="gpt-4-turbo",
    temperature=0.1,
    max_tokens=4000,
    verbose=True,
    memory_enabled=True,
    security_level="high"
)

# Stateful agent with memory
stateful_agent = StatefulGeopoliticalAgent(
    user_id="analyst_001",
    memory_backend="langgraph",
    conversation_timeout=3600
)
```

### 🔧 Tool Configuration
```python
# Advanced tool setup with custom parameters
tools = [
    SerperSearchTool(
        num_results=20,
        time_range="m1",
        location="us"
    ),
    Crawl4AITool(
        focus_keywords=["security", "intelligence", "threat"],
        max_depth=3,
        extract_images=True
    ),
    MultiModalAnalyzer(
        analysis_types=["metadata", "ocr", "faces", "objects"],
        confidence_threshold=0.8
    ),
    BrowserAutomation(
        browser_type="chrome",
        headless=True,
        stealth=True
    )
]
```

### 🔄 Workflow Configuration
```python
# Advanced workflow customization
workflow = GeopoliticalWorkflow(
    llm_model="gpt-4-turbo",
    temperature=0.1,
    verbose=True,
    parallel_execution=True,
    max_concurrent_tasks=5,
    timeout_seconds=1800
)

# Event-driven workflow with scheduling
builder = WorkflowBuilder("custom_analysis", "Custom analysis pipeline")
builder.set_timeout(3600).set_max_concurrent_executions(3)
```

## 📈 Performance Monitoring & Evaluation

### 🧪 Comprehensive Evaluation Framework
```python
from workflows.dspy_evaluator import DSPyEvaluator
from utils.monitoring import performance_monitor

# Performance evaluation
evaluator = DSPyEvaluator()
evaluation_results = evaluator.evaluate_analysis_quality(
    domain="geopolitical",
    query="Regional stability assessment",
    analysis=analysis_result
)

# Real-time performance monitoring
@performance_monitor
def analyze_with_monitoring(query):
    return agent.analyze(query)

# System metrics
metrics = {
    "response_time": "Average API response time",
    "accuracy_score": "Analysis accuracy metrics",
    "throughput": "Requests processed per minute",
    "error_rate": "Error percentage",
    "resource_usage": "CPU, memory, disk usage"
}
```

### 📊 Quality Assurance Features
- **Automated Testing** - Continuous integration with comprehensive test suites
- **Quality Metrics** - Accuracy, completeness, relevance scoring
- **Prompt Optimization** - Automated improvement using DSPy
- **Comparative Analysis** - A/B testing of different approaches
- **Synthetic Data Generation** - Create evaluation datasets
- **Performance Benchmarking** - Regular performance assessments

## 🐳 Docker Deployment

### Quick Start with Docker Compose
```bash
# Clone and navigate to project
git clone <repository-url>
cd crewai-osint-framework

# Configure environment
cp .env.example .env
# Edit .env with your API keys

# Deploy with Docker Compose
docker-compose up -d

# Access services
# API: http://localhost:8000
# UI: http://localhost:8501
# Metrics: http://localhost:9090
```

### Production Deployment
```bash
# Build production image
docker build -f Dockerfile -t osint-framework:latest .

# Run with production settings
docker run -d \
  --name osint-framework \
  -p 8000:8000 \
  -p 9090:9090 \
  -e ENVIRONMENT=production \
  -e LOG_LEVEL=INFO \
  -v ./data:/app/data \
  -v ./logs:/app/logs \
  osint-framework:latest
```

## 🧪 Testing & Quality Assurance

### Comprehensive Testing Suite
```bash
# Run all tests
python scripts/run_tests.py

# Run specific test categories
python scripts/run_tests.py --suite "Agent Tests"
python scripts/run_tests.py --suite "Tools Tests"
python scripts/run_tests.py --suite "Workflow Tests"
python scripts/run_tests.py --suite "Security Tests"

# Run with coverage
python scripts/run_tests.py --coverage

# Performance testing
python scripts/run_tests.py --performance
```

### Validation & Setup
```bash
# Validate complete setup
python validate_setup.py

# Check system dependencies
python validate_setup.py --check-deps

# Test API connectivity
python validate_setup.py --test-apis
```

## 🤝 Contributing

### Development Setup
```bash
# Fork and clone the repository
git clone https://github.com/your-username/crewai-osint-framework.git
cd crewai-osint-framework

# Set up development environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run development server
python -m uvicorn api.main:app --reload
```

### Contribution Guidelines
1. **Fork the repository** and create a feature branch
2. **Follow code standards** - Black formatting, isort imports, flake8 linting
3. **Write comprehensive tests** for new functionality
4. **Update documentation** including docstrings and README
5. **Ensure security compliance** - No hardcoded secrets, proper input validation
6. **Submit pull request** with detailed description

### Code Quality Standards
```bash
# Format code
black .
isort .

# Lint code
flake8 .
pylint agents/ tools/ workflows/

# Type checking
mypy agents/ tools/ workflows/

# Security scanning
bandit -r .
```

## 📄 License & Legal

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### Third-Party Licenses
- **CrewAI** - Apache 2.0 License
- **LangChain** - MIT License
- **LlamaIndex** - MIT License
- **DSPy** - Apache 2.0 License
- **FastAPI** - MIT License

## 🙏 Acknowledgments

### Core Frameworks
- **[CrewAI](https://github.com/joaomdmoura/crewAI)** - Multi-agent orchestration framework
- **[LangChain](https://github.com/hwchase17/langchain)** - LLM application development framework
- **[LangGraph](https://github.com/langchain-ai/langgraph)** - Stateful workflow management
- **[LlamaIndex](https://github.com/run-llama/llama_index)** - Data framework for LLM applications
- **[DSPy](https://github.com/stanfordnlp/dspy)** - Programming framework for LLMs

### External Services
- **[OpenAI](https://openai.com/)** - Language model capabilities
- **[Serper](https://serper.dev/)** - Google Search API
- **[Crawl4AI](https://github.com/unclecode/crawl4ai)** - Web crawling and extraction

### Development Tools
- **[FastAPI](https://fastapi.tiangolo.com/)** - Modern web framework for APIs
- **[Streamlit](https://streamlit.io/)** - Interactive web applications
- **[Docker](https://www.docker.com/)** - Containerization platform

## 📞 Support & Community

### Documentation
- **[API Documentation](docs/API_DOCUMENTATION.md)** - Complete API reference
- **[Developer Guide](docs/DEVELOPER_GUIDE.md)** - Development and contribution guide
- **[User Guide](docs/USER_GUIDE.md)** - End-user documentation
- **[Deployment Guide](docs/DEPLOYMENT_GUIDE.md)** - Production deployment guide

### Community & Support
- **GitHub Issues** - Bug reports and feature requests
- **GitHub Discussions** - Community discussions and Q&A
- **Documentation** - Comprehensive guides and examples
- **Examples Directory** - Usage patterns and demonstrations

### Security & Responsible Use
- **Security Policy** - Report security vulnerabilities responsibly
- **Code of Conduct** - Community guidelines and standards
- **Privacy Policy** - Data handling and privacy practices

---

**⚠️ Important Disclaimer**: This framework is designed for **authorized security research, threat intelligence, and educational purposes only**. Users are responsible for ensuring compliance with applicable laws, regulations, and ethical guidelines when conducting OSINT activities. Always obtain proper authorization before investigating targets and respect privacy rights and terms of service.