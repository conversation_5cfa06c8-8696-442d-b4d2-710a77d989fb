"""
🧠 Enhanced OSINT Agent Framework - Streamlit UI

Advanced interactive web interface with comprehensive agent monitoring,
customization, and real-time process tracking capabilities.

Features:
- Dynamic agent selection and configuration
- Real-time agent process monitoring
- Custom system prompts and tool settings
- Rich markdown output rendering
- Comprehensive file save options
- Agent workflow visualization
"""

import json
import os
import sys
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
import queue

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from streamlit.runtime.scriptrunner import add_script_run_ctx

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import agents and tools
from agents.base_agent import BaseOSINTAgent
from agents.cti_agent import CTIAgent
from agents.geo_agent import GeopoliticalAgent
from agents.browser_osint_agent import BrowserOSINTAgent
from agents.multimodal_osint_agent import MultiModalOSINTAgent

# Page configuration
st.set_page_config(
    page_title="🧠 Enhanced OSINT Framework",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Custom CSS for enhanced UI
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    .agent-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin: 1rem 0;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    .process-step {
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 0 5px 5px 0;
    }
    .process-step.active {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
        animation: pulse 2s infinite;
    }
    .process-step.completed {
        background-color: #e8f5e8;
        border-left-color: #4caf50;
    }
    .process-step.error {
        background-color: #ffebee;
        border-left-color: #f44336;
    }
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #e0e0e0;
        margin: 0.5rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .tool-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .tool-enabled {
        background-color: #d4edda;
        color: #155724;
    }
    .tool-disabled {
        background-color: #f8d7da;
        color: #721c24;
    }
    .monitoring-panel {
        background-color: #1e1e1e;
        color: #ffffff;
        padding: 1rem;
        border-radius: 5px;
        font-family: 'Courier New', monospace;
        max-height: 400px;
        overflow-y: auto;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize comprehensive session state for enhanced UI."""
    # Core application state
    if "selected_agent_type" not in st.session_state:
        st.session_state.selected_agent_type = "CTI Agent"
    
    if "current_agent" not in st.session_state:
        st.session_state.current_agent = None
    
    if "agent_config" not in st.session_state:
        st.session_state.agent_config = {
            "llm_model": "gpt-4",
            "temperature": 0.1,
            "max_tokens": 2000,
            "verbose": True
        }
    
    # Agent customization
    if "custom_role" not in st.session_state:
        st.session_state.custom_role = ""
    
    if "custom_goal" not in st.session_state:
        st.session_state.custom_goal = ""
    
    if "custom_backstory" not in st.session_state:
        st.session_state.custom_backstory = ""
    
    # Tool configuration
    if "tool_config" not in st.session_state:
        st.session_state.tool_config = {
            "enable_web_search": True,
            "enable_web_crawling": True,
            "enable_rag": True,
            "search_timeout": 30,
            "max_search_results": 10
        }
    
    # Process monitoring
    if "process_log" not in st.session_state:
        st.session_state.process_log = []
    
    if "current_task_status" not in st.session_state:
        st.session_state.current_task_status = "idle"
    
    if "task_progress" not in st.session_state:
        st.session_state.task_progress = 0
    
    # Results and history
    if "task_results" not in st.session_state:
        st.session_state.task_results = []
    
    if "execution_history" not in st.session_state:
        st.session_state.execution_history = []

def get_available_agents() -> Dict[str, Dict[str, Any]]:
    """Get information about all available agents."""
    return {
        "CTI Agent": {
            "class": CTIAgent,
            "description": "Cyber Threat Intelligence analysis and IOC extraction",
            "icon": "🔒",
            "capabilities": ["IOC Extraction", "Threat Actor Tracking", "Campaign Analysis"],
            "default_role": "Senior Cyber Threat Intelligence Analyst",
            "default_goal": "Extract, analyze, and correlate cyber threat intelligence from various sources",
            "default_backstory": "You are an expert CTI analyst with years of experience in threat hunting and malware analysis."
        },
        "Geopolitical Agent": {
            "class": GeopoliticalAgent,
            "description": "Geopolitical analysis and intelligence briefing",
            "icon": "🌍",
            "capabilities": ["Regional Analysis", "Intelligence Briefing", "Situation Reports"],
            "default_role": "Senior Geopolitical Intelligence Analyst",
            "default_goal": "Analyze geopolitical developments and provide strategic intelligence assessments",
            "default_backstory": "You are a seasoned geopolitical analyst with deep expertise in international relations and regional dynamics."
        },
        "Browser OSINT Agent": {
            "class": BrowserOSINTAgent,
            "description": "Web-based OSINT collection and analysis",
            "icon": "🌐",
            "capabilities": ["Web Scraping", "Social Media Analysis", "Domain Investigation"],
            "default_role": "OSINT Web Intelligence Specialist",
            "default_goal": "Collect and analyze open source intelligence from web sources",
            "default_backstory": "You are an expert in web-based intelligence gathering with advanced skills in digital forensics."
        },
        "Multimodal OSINT Agent": {
            "class": MultiModalOSINTAgent,
            "description": "Image, video, and multimedia content analysis",
            "icon": "📸",
            "capabilities": ["Image Analysis", "Video Processing", "OCR", "Face Recognition"],
            "default_role": "Multimedia Intelligence Analyst",
            "default_goal": "Analyze multimedia content for intelligence extraction",
            "default_backstory": "You are a specialist in multimedia analysis with expertise in computer vision and content forensics."
        }
    }

def display_header():
    """Display the enhanced main header."""
    st.markdown('<h1 class="main-header">🧠 Enhanced OSINT Agent Framework</h1>', unsafe_allow_html=True)
    st.markdown("**Advanced AI-Powered Intelligence Analysis with Real-Time Monitoring**")
    
    # API Key status with enhanced display
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        openai_key = os.getenv("OPENAI_API_KEY")
        serper_key = os.getenv("SERPER_API_KEY")
        
        if openai_key and serper_key:
            st.success("✅ All API Keys Configured")
        elif openai_key:
            st.warning("⚠️ OpenAI configured, Serper missing")
        elif serper_key:
            st.warning("⚠️ Serper configured, OpenAI missing")
        else:
            st.error("❌ API Keys not configured")
    
    with col2:
        if st.button("🔄 Refresh Status"):
            st.rerun()
    
    with col3:
        if st.button("⚙️ Settings"):
            st.session_state.show_settings = True

def sidebar_navigation():
    """Enhanced sidebar navigation with agent selection."""
    st.sidebar.title("🎯 Agent Control Center")
    
    # Agent Selection
    st.sidebar.markdown("### 🤖 Select Agent")
    agents = get_available_agents()
    
    agent_options = [f"{info['icon']} {name}" for name, info in agents.items()]
    selected_display = st.sidebar.selectbox("Choose Agent Type", agent_options)
    
    # Extract agent name from display
    selected_agent = selected_display.split(" ", 1)[1]
    st.session_state.selected_agent_type = selected_agent
    
    # Display agent info
    agent_info = agents[selected_agent]
    st.sidebar.markdown(f"**{agent_info['description']}**")
    
    # Capabilities
    st.sidebar.markdown("**Capabilities:**")
    for cap in agent_info['capabilities']:
        st.sidebar.markdown(f"• {cap}")
    
    st.sidebar.markdown("---")
    
    # Navigation
    page = st.sidebar.selectbox(
        "Navigate to",
        [
            "🏠 Agent Dashboard",
            "🔧 Agent Configuration", 
            "📋 Task Execution",
            "📊 Process Monitor",
            "📈 Analytics",
            "💾 Results & Export"
        ]
    )
    
    return page

def agent_dashboard_page():
    """Main agent dashboard with overview and quick actions."""
    st.markdown("## 🏠 Agent Dashboard")
    
    agents = get_available_agents()
    selected_agent_info = agents[st.session_state.selected_agent_type]
    
    # Agent overview card
    st.markdown(f"""
    <div class="agent-card">
        <h3>{selected_agent_info['icon']} {st.session_state.selected_agent_type}</h3>
        <p>{selected_agent_info['description']}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Quick stats
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Agent Status", "Ready" if st.session_state.current_agent else "Not Initialized")
    
    with col2:
        st.metric("Tasks Completed", len(st.session_state.execution_history))
    
    with col3:
        st.metric("Tools Available", len(selected_agent_info['capabilities']))
    
    with col4:
        avg_time = 0
        if st.session_state.execution_history:
            times = [h.get('execution_time', 0) for h in st.session_state.execution_history]
            avg_time = sum(times) / len(times)
        st.metric("Avg. Execution Time", f"{avg_time:.1f}s")
    
    # Quick actions
    st.markdown("### 🚀 Quick Actions")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔧 Configure Agent", use_container_width=True):
            st.session_state.current_page = "🔧 Agent Configuration"
            st.rerun()
    
    with col2:
        if st.button("📋 Execute Task", use_container_width=True):
            st.session_state.current_page = "📋 Task Execution"
            st.rerun()
    
    with col3:
        if st.button("📊 Monitor Process", use_container_width=True):
            st.session_state.current_page = "📊 Process Monitor"
            st.rerun()
    
    # Recent activity
    if st.session_state.execution_history:
        st.markdown("### 📋 Recent Activity")
        
        for i, execution in enumerate(st.session_state.execution_history[-3:]):
            with st.expander(f"Task {len(st.session_state.execution_history) - i}: {execution.get('task', 'Unknown')[:50]}..."):
                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"**Status:** {execution.get('status', 'Unknown')}")
                    st.write(f"**Duration:** {execution.get('execution_time', 0):.2f}s")
                with col2:
                    st.write(f"**Timestamp:** {execution.get('timestamp', 'Unknown')}")
                    st.write(f"**Agent:** {execution.get('agent', 'Unknown')}")

def agent_configuration_page():
    """Agent configuration page with system prompt customization."""
    st.markdown("## 🔧 Agent Configuration")

    agents = get_available_agents()
    selected_agent_info = agents[st.session_state.selected_agent_type]

    st.markdown(f"### Configuring: {selected_agent_info['icon']} {st.session_state.selected_agent_type}")

    # Configuration tabs
    tab1, tab2, tab3, tab4 = st.tabs(["🤖 Agent Settings", "🛠️ Tool Configuration", "🧠 LLM Settings", "💾 Save/Load"])

    with tab1:
        st.markdown("#### Agent Personality & Behavior")

        # Use custom values if set, otherwise use defaults
        default_role = st.session_state.custom_role or selected_agent_info['default_role']
        default_goal = st.session_state.custom_goal or selected_agent_info['default_goal']
        default_backstory = st.session_state.custom_backstory or selected_agent_info['default_backstory']

        # Agent role customization
        st.session_state.custom_role = st.text_area(
            "Agent Role",
            value=default_role,
            help="Define the agent's role and expertise area",
            height=100
        )

        # Agent goal customization
        st.session_state.custom_goal = st.text_area(
            "Agent Goal",
            value=default_goal,
            help="Specify what the agent should accomplish",
            height=100
        )

        # Agent backstory customization
        st.session_state.custom_backstory = st.text_area(
            "Agent Backstory",
            value=default_backstory,
            help="Provide context and background for the agent",
            height=150
        )

        # Reset to defaults button
        if st.button("🔄 Reset to Defaults"):
            st.session_state.custom_role = selected_agent_info['default_role']
            st.session_state.custom_goal = selected_agent_info['default_goal']
            st.session_state.custom_backstory = selected_agent_info['default_backstory']
            st.rerun()

    with tab2:
        st.markdown("#### Tool Configuration")

        # Tool enable/disable toggles
        col1, col2 = st.columns(2)

        with col1:
            st.session_state.tool_config["enable_web_search"] = st.checkbox(
                "🔍 Web Search (Serper)",
                value=st.session_state.tool_config["enable_web_search"],
                help="Enable web search capabilities"
            )

            st.session_state.tool_config["enable_web_crawling"] = st.checkbox(
                "🕷️ Web Crawling",
                value=st.session_state.tool_config["enable_web_crawling"],
                help="Enable web page crawling and content extraction"
            )

            st.session_state.tool_config["enable_rag"] = st.checkbox(
                "📚 RAG (Retrieval Augmented Generation)",
                value=st.session_state.tool_config["enable_rag"],
                help="Enable document retrieval and knowledge base querying"
            )

        with col2:
            st.session_state.tool_config["search_timeout"] = st.slider(
                "Search Timeout (seconds)",
                min_value=10,
                max_value=120,
                value=st.session_state.tool_config["search_timeout"],
                help="Maximum time to wait for search results"
            )

            st.session_state.tool_config["max_search_results"] = st.slider(
                "Max Search Results",
                min_value=5,
                max_value=50,
                value=st.session_state.tool_config["max_search_results"],
                help="Maximum number of search results to retrieve"
            )

        # Tool status display
        st.markdown("#### Current Tool Status")

        tools_status = [
            ("Web Search", st.session_state.tool_config["enable_web_search"], "🔍"),
            ("Web Crawling", st.session_state.tool_config["enable_web_crawling"], "🕷️"),
            ("RAG", st.session_state.tool_config["enable_rag"], "📚")
        ]

        for tool_name, enabled, icon in tools_status:
            status_class = "tool-enabled" if enabled else "tool-disabled"
            status_text = "Enabled" if enabled else "Disabled"
            st.markdown(f'{icon} {tool_name}: <span class="{status_class}">{status_text}</span>',
                       unsafe_allow_html=True)

    with tab3:
        st.markdown("#### Language Model Settings")

        col1, col2 = st.columns(2)

        with col1:
            st.session_state.agent_config["llm_model"] = st.selectbox(
                "LLM Model",
                ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
                index=["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"].index(st.session_state.agent_config["llm_model"])
            )

            st.session_state.agent_config["temperature"] = st.slider(
                "Temperature",
                min_value=0.0,
                max_value=1.0,
                value=st.session_state.agent_config["temperature"],
                step=0.1,
                help="Controls randomness in responses (0.0 = deterministic, 1.0 = very random)"
            )

        with col2:
            st.session_state.agent_config["max_tokens"] = st.slider(
                "Max Tokens",
                min_value=500,
                max_value=4000,
                value=st.session_state.agent_config["max_tokens"],
                step=100,
                help="Maximum number of tokens in the response"
            )

            st.session_state.agent_config["verbose"] = st.checkbox(
                "Verbose Logging",
                value=st.session_state.agent_config["verbose"],
                help="Enable detailed logging output"
            )

    with tab4:
        st.markdown("#### Configuration Management")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Save Configuration**")
            config_name = st.text_input("Configuration Name", placeholder="my_custom_config")

            col1a, col1b = st.columns(2)
            with col1a:
                save_to_file = st.checkbox("💾 Save to File", value=True, help="Save configuration to file for persistence")
            with col1b:
                save_to_session = st.checkbox("🔄 Save to Session", value=True, help="Save to current session")

            if st.button("💾 Save Configuration"):
                if config_name:
                    config_data = {
                        "agent_type": st.session_state.selected_agent_type,
                        "custom_role": st.session_state.custom_role,
                        "custom_goal": st.session_state.custom_goal,
                        "custom_backstory": st.session_state.custom_backstory,
                        "agent_config": st.session_state.agent_config,
                        "tool_config": st.session_state.tool_config,
                        "timestamp": datetime.now().isoformat()
                    }

                    # Save to file if requested
                    if save_to_file:
                        filepath = save_configuration_to_file(config_name, config_data)
                        if filepath:
                            st.info(f"📁 Saved to: {filepath}")

                    # Save to session state if requested
                    if save_to_session:
                        if "saved_configs" not in st.session_state:
                            st.session_state.saved_configs = {}
                        st.session_state.saved_configs[config_name] = config_data
                        st.success(f"Configuration '{config_name}' saved to session!")
                else:
                    st.error("Please enter a configuration name")

        with col2:
            st.markdown("**Load Configuration**")

            # Load from file or session
            load_source = st.radio("Load from:", ["Session", "File"], horizontal=True)

            if load_source == "Session":
                if "saved_configs" in st.session_state and st.session_state.saved_configs:
                    selected_config = st.selectbox(
                        "Select Configuration",
                        list(st.session_state.saved_configs.keys())
                    )

                    if st.button("📂 Load from Session"):
                        config_data = st.session_state.saved_configs[selected_config]
                        load_configuration_data(config_data)
                        st.success(f"Configuration '{selected_config}' loaded from session!")
                        st.rerun()
                else:
                    st.info("No session configurations available")

            else:  # Load from file
                saved_configs = get_saved_configurations()
                if saved_configs:
                    config_options = [f"{config['name']} ({config['agent_type']})" for config in saved_configs]
                    selected_idx = st.selectbox(
                        "Select Configuration File",
                        range(len(config_options)),
                        format_func=lambda x: config_options[x]
                    )

                    if st.button("📂 Load from File"):
                        config_data = saved_configs[selected_idx]["config_data"]
                        load_configuration_data(config_data)
                        st.success(f"Configuration loaded from file!")
                        st.rerun()

                    # Show config details
                    if selected_idx is not None:
                        config = saved_configs[selected_idx]
                        st.caption(f"Saved: {config['saved_timestamp'][:10]}")
                else:
                    st.info("No saved configuration files found")

    # Initialize agent button
    st.markdown("---")
    if st.button("🚀 Initialize Agent with Current Configuration", type="primary", use_container_width=True):
        with st.spinner("Initializing agent..."):
            try:
                # Get agent class
                agent_class = selected_agent_info['class']

                # Create agent with custom configuration
                st.session_state.current_agent = agent_class(
                    name=f"custom_{st.session_state.selected_agent_type.lower().replace(' ', '_')}",
                    role=st.session_state.custom_role,
                    goal=st.session_state.custom_goal,
                    backstory=st.session_state.custom_backstory,
                    llm_model=st.session_state.agent_config["llm_model"],
                    temperature=st.session_state.agent_config["temperature"],
                    max_tokens=st.session_state.agent_config["max_tokens"],
                    verbose=st.session_state.agent_config["verbose"]
                )

                st.success("✅ Agent initialized successfully!")
                st.balloons()

            except Exception as e:
                st.error(f"❌ Failed to initialize agent: {str(e)}")

def task_execution_page():
    """Task execution page with templates and examples."""
    st.markdown("## 📋 Task Execution")

    if not st.session_state.current_agent:
        st.warning("⚠️ Please configure and initialize an agent first!")
        if st.button("🔧 Go to Configuration"):
            st.session_state.current_page = "🔧 Agent Configuration"
            st.rerun()
        return

    # Task input section
    st.markdown("### 📝 Task Definition")

    # Task templates
    templates = {
        "CTI Agent": [
            "Analyze this threat report and extract all IOCs: [paste threat report here]",
            "Track the threat actor APT28 and provide a comprehensive profile",
            "Correlate these IOCs with known campaigns: [list IOCs]",
            "Generate a threat intelligence report on recent ransomware activities"
        ],
        "Geopolitical Agent": [
            "Provide an intelligence brief on current tensions in the Middle East",
            "Analyze the geopolitical implications of recent trade agreements",
            "Monitor regional developments in Eastern Europe for the past 7 days",
            "Generate a situation report on Asia-Pacific security dynamics"
        ],
        "Browser OSINT Agent": [
            "Investigate the domain example.com for suspicious activities",
            "Collect OSINT on the social media presence of [target]",
            "Analyze the website structure and technologies used by [URL]",
            "Gather intelligence on the organization [company name]"
        ],
        "Multimodal OSINT Agent": [
            "Analyze this image for any identifying information or metadata",
            "Extract text from this document image using OCR",
            "Identify faces and objects in this surveillance footage",
            "Analyze the geolocation clues in this photograph"
        ]
    }

    # Template selection
    agent_type = st.session_state.selected_agent_type
    if agent_type in templates:
        st.markdown("#### 📋 Task Templates")
        selected_template = st.selectbox(
            "Choose a template (optional)",
            ["Custom Task"] + templates[agent_type]
        )

        if selected_template != "Custom Task":
            if st.button("📋 Use Template"):
                st.session_state.task_input = selected_template
                st.rerun()

    # Task input
    task_input = st.text_area(
        "Task Description",
        value=getattr(st.session_state, 'task_input', ''),
        placeholder="Describe the task you want the agent to perform...",
        height=150,
        help="Provide a detailed description of what you want the agent to analyze or investigate"
    )

    # Additional context
    context_input = st.text_area(
        "Additional Context (Optional)",
        placeholder="Provide any additional context, constraints, or specific requirements...",
        height=100
    )

    # Execution options
    col1, col2, col3 = st.columns(3)

    with col1:
        enable_monitoring = st.checkbox("📊 Enable Real-time Monitoring", value=True)

    with col2:
        auto_save = st.checkbox("💾 Auto-save Results", value=True)

    with col3:
        detailed_logging = st.checkbox("📝 Detailed Logging", value=True)

    # Execute task button
    if st.button("🚀 Execute Task", type="primary", use_container_width=True):
        if task_input.strip():
            # Store task input
            st.session_state.task_input = task_input

            # Prepare context
            context = {}
            if context_input.strip():
                context["additional_context"] = context_input

            context["execution_options"] = {
                "enable_monitoring": enable_monitoring,
                "auto_save": auto_save,
                "detailed_logging": detailed_logging
            }

            # Execute task with monitoring
            execute_task_with_monitoring(task_input, context)
        else:
            st.error("Please provide a task description")

def execute_task_with_monitoring(task_description: str, context: Dict[str, Any]):
    """Execute task with real-time monitoring."""
    # Initialize monitoring
    st.session_state.current_task_status = "running"
    st.session_state.task_progress = 0
    st.session_state.process_log = []

    # Create monitoring containers
    progress_container = st.container()
    log_container = st.container()

    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()

    with log_container:
        st.markdown("### 📊 Real-time Process Monitor")
        log_display = st.empty()

    def log_process_step(step: str, status: str = "active", details: str = ""):
        """Log a process step."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = {
            "timestamp": timestamp,
            "step": step,
            "status": status,
            "details": details
        }
        st.session_state.process_log.append(log_entry)

        # Update display
        log_html = generate_process_log_html(st.session_state.process_log)
        log_display.markdown(log_html, unsafe_allow_html=True)

    try:
        # Step 1: Initialize
        log_process_step("Initializing task execution", "active")
        progress_bar.progress(10)
        status_text.text("🔄 Initializing...")
        time.sleep(1)

        log_process_step("Initializing task execution", "completed", "Agent ready for task")

        # Step 2: Prepare tools
        log_process_step("Preparing agent tools", "active")
        progress_bar.progress(20)
        status_text.text("🛠️ Preparing tools...")
        time.sleep(1)

        log_process_step("Preparing agent tools", "completed", f"Loaded {len(st.session_state.current_agent.tools)} tools")

        # Step 3: Execute task
        log_process_step("Executing main task", "active")
        progress_bar.progress(30)
        status_text.text("🧠 Agent thinking...")

        # Actually execute the task
        start_time = datetime.now()
        result = st.session_state.current_agent.execute_task(task_description, context)
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        progress_bar.progress(80)
        log_process_step("Executing main task", "completed", f"Task completed in {execution_time:.2f}s")

        # Step 4: Process results
        log_process_step("Processing results", "active")
        progress_bar.progress(90)
        status_text.text("📊 Processing results...")
        time.sleep(0.5)

        log_process_step("Processing results", "completed", "Results formatted and ready")

        # Step 5: Complete
        progress_bar.progress(100)
        status_text.text("✅ Task completed successfully!")
        log_process_step("Task execution completed", "completed", f"Total execution time: {execution_time:.2f}s")

        # Store results
        st.session_state.task_results.append(result)
        st.session_state.execution_history.append({
            "task": task_description,
            "result": result,
            "execution_time": execution_time,
            "timestamp": end_time.isoformat(),
            "status": "success"
        })

        st.session_state.current_task_status = "completed"

        # Display results
        st.markdown("### 📄 Task Results")
        st.markdown(result.get('result', 'No result available'))

        # Auto-save if enabled
        if context.get("execution_options", {}).get("auto_save", False):
            save_result_to_file(result, task_description)

    except Exception as e:
        log_process_step("Task execution", "error", f"Error: {str(e)}")
        st.error(f"❌ Task execution failed: {str(e)}")
        st.session_state.current_task_status = "error"

def generate_process_log_html(log_entries: List[Dict[str, Any]]) -> str:
    """Generate HTML for process log display."""
    html = '<div class="monitoring-panel">'

    for entry in log_entries[-10:]:  # Show last 10 entries
        status_class = f"process-step {entry['status']}"
        icon = {"active": "🔄", "completed": "✅", "error": "❌"}.get(entry['status'], "ℹ️")

        html += f'''
        <div class="{status_class}">
            <strong>{entry['timestamp']} {icon} {entry['step']}</strong>
            {f"<br><small>{entry['details']}</small>" if entry['details'] else ""}
        </div>
        '''

    html += '</div>'
    return html

def save_result_to_file(result: Dict[str, Any], task_description: str):
    """Save task result to file with enhanced formatting."""
    try:
        # Create output directory structure
        output_dir = Path("output/results")
        output_dir.mkdir(parents=True, exist_ok=True)

        # Create date-based subdirectory
        date_dir = output_dir / datetime.now().strftime("%Y-%m-%d")
        date_dir.mkdir(exist_ok=True)

        # Generate filename with agent type
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        agent_name = result.get('agent', 'unknown').replace(' ', '_').lower()
        filename = f"{agent_name}_task_{timestamp}.md"
        filepath = date_dir / filename

        # Format content with enhanced structure
        content = f"""# OSINT Task Result Report

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**Agent:** {result.get('agent', 'Unknown')}
**Status:** {result.get('status', 'Unknown')}
**Execution Time:** {result.get('execution_time', 0):.2f} seconds

---

## 📋 Task Description

{task_description}

---

## 🔍 Analysis Results

{result.get('result', 'No result available')}

---

## 📊 Execution Metadata

| Field | Value |
|-------|-------|
| Agent Type | {result.get('agent', 'Unknown')} |
| Task ID | {result.get('task_id', 'N/A')} |
| Start Time | {result.get('timestamp', 'Unknown')} |
| Duration | {result.get('execution_time', 0):.2f} seconds |
| Status | {result.get('status', 'Unknown')} |
| Tools Used | {', '.join(result.get('tools_used', []))} |

---

## 🔧 Configuration Used

```json
{json.dumps(result.get('context', {}), indent=2)}
```

---

*Report generated by Enhanced OSINT Framework*
"""

        # Save file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        # Also save as JSON for programmatic access
        json_filepath = filepath.with_suffix('.json')
        with open(json_filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, default=str)

        st.success(f"✅ Results saved to:")
        st.code(f"📄 Markdown: {filepath}")
        st.code(f"📊 JSON: {json_filepath}")

        # Add to session state for tracking
        if "saved_files" not in st.session_state:
            st.session_state.saved_files = []
        st.session_state.saved_files.append({
            "filepath": str(filepath),
            "json_filepath": str(json_filepath),
            "timestamp": datetime.now().isoformat(),
            "task": task_description[:50] + "..." if len(task_description) > 50 else task_description
        })

    except Exception as e:
        st.error(f"❌ Failed to save results: {str(e)}")

def save_configuration_to_file(config_name: str, config_data: Dict[str, Any]):
    """Save agent configuration to file."""
    try:
        # Create config directory
        config_dir = Path("config/saved_configs")
        config_dir.mkdir(parents=True, exist_ok=True)

        # Generate filename
        safe_name = "".join(c for c in config_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"{safe_name.replace(' ', '_')}.json"
        filepath = config_dir / filename

        # Add metadata
        config_data["saved_timestamp"] = datetime.now().isoformat()
        config_data["config_version"] = "1.0"

        # Save configuration
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)

        st.success(f"✅ Configuration saved to {filepath}")
        return str(filepath)

    except Exception as e:
        st.error(f"❌ Failed to save configuration: {str(e)}")
        return None

def load_configuration_from_file(filepath: str) -> Optional[Dict[str, Any]]:
    """Load agent configuration from file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            config_data = json.load(f)

        st.success(f"✅ Configuration loaded from {filepath}")
        return config_data

    except Exception as e:
        st.error(f"❌ Failed to load configuration: {str(e)}")
        return None

def get_saved_configurations() -> List[Dict[str, Any]]:
    """Get list of saved configurations."""
    config_dir = Path("config/saved_configs")
    if not config_dir.exists():
        return []

    configs = []
    for config_file in config_dir.glob("*.json"):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            configs.append({
                "name": config_file.stem.replace('_', ' '),
                "filepath": str(config_file),
                "agent_type": config_data.get("agent_type", "Unknown"),
                "saved_timestamp": config_data.get("saved_timestamp", "Unknown"),
                "config_data": config_data
            })
        except Exception:
            continue  # Skip corrupted files

    return sorted(configs, key=lambda x: x["saved_timestamp"], reverse=True)

def load_configuration_data(config_data: Dict[str, Any]):
    """Load configuration data into session state."""
    st.session_state.selected_agent_type = config_data.get("agent_type", "CTI Agent")
    st.session_state.custom_role = config_data.get("custom_role", "")
    st.session_state.custom_goal = config_data.get("custom_goal", "")
    st.session_state.custom_backstory = config_data.get("custom_backstory", "")
    st.session_state.agent_config = config_data.get("agent_config", {
        "llm_model": "gpt-4",
        "temperature": 0.1,
        "max_tokens": 2000,
        "verbose": True
    })
    st.session_state.tool_config = config_data.get("tool_config", {
        "enable_web_search": True,
        "enable_web_crawling": True,
        "enable_rag": True,
        "search_timeout": 30,
        "max_search_results": 10
    })

def process_monitor_page():
    """Real-time process monitoring page."""
    st.markdown("## 📊 Process Monitor")

    # Current status overview
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        status_color = {
            "idle": "🟢",
            "running": "🟡",
            "completed": "✅",
            "error": "🔴"
        }.get(st.session_state.current_task_status, "⚪")
        st.metric("Current Status", f"{status_color} {st.session_state.current_task_status.title()}")

    with col2:
        st.metric("Progress", f"{st.session_state.task_progress}%")

    with col3:
        st.metric("Total Tasks", len(st.session_state.execution_history))

    with col4:
        if st.session_state.execution_history:
            success_rate = len([h for h in st.session_state.execution_history if h.get('status') == 'success']) / len(st.session_state.execution_history) * 100
            st.metric("Success Rate", f"{success_rate:.1f}%")
        else:
            st.metric("Success Rate", "N/A")

    # Real-time log display
    st.markdown("### 📋 Real-time Process Log")

    if st.session_state.process_log:
        # Auto-refresh toggle
        auto_refresh = st.checkbox("🔄 Auto-refresh (every 2 seconds)", value=False)

        if auto_refresh:
            time.sleep(2)
            st.rerun()

        # Display process log
        log_html = generate_process_log_html(st.session_state.process_log)
        st.markdown(log_html, unsafe_allow_html=True)

        # Clear log button
        if st.button("🗑️ Clear Log"):
            st.session_state.process_log = []
            st.rerun()
    else:
        st.info("No process activity to display. Execute a task to see real-time monitoring.")

    # Process timeline visualization
    if st.session_state.execution_history:
        st.markdown("### 📈 Execution Timeline")

        # Create timeline data
        timeline_data = []
        for i, execution in enumerate(st.session_state.execution_history):
            timeline_data.append({
                "Task": f"Task {i+1}",
                "Duration": execution.get('execution_time', 0),
                "Status": execution.get('status', 'unknown'),
                "Timestamp": execution.get('timestamp', '')
            })

        df = pd.DataFrame(timeline_data)

        # Create timeline chart
        fig = px.bar(
            df,
            x="Task",
            y="Duration",
            color="Status",
            title="Task Execution Timeline",
            labels={"Duration": "Execution Time (seconds)"}
        )
        st.plotly_chart(fig, use_container_width=True)

    # Agent performance metrics
    if st.session_state.current_agent:
        st.markdown("### 🤖 Agent Performance")

        capabilities = st.session_state.current_agent.get_capabilities()

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Agent Information**")
            st.write(f"**Name:** {capabilities.get('name', 'Unknown')}")
            st.write(f"**Role:** {capabilities.get('role', 'Unknown')}")
            st.write(f"**Status:** {capabilities.get('initialization_status', 'Unknown')}")

        with col2:
            st.markdown("**Tool Status**")
            st.write(f"**Available Tools:** {capabilities.get('tool_count', 0)}")
            st.write(f"**Knowledge Base:** {'✅' if capabilities.get('has_knowledge_base') else '❌'}")
            st.write(f"**LLM Model:** {capabilities.get('llm_model', 'Unknown')}")

def analytics_page():
    """Analytics and insights page."""
    st.markdown("## 📈 Analytics & Insights")

    if not st.session_state.execution_history:
        st.info("No execution data available yet. Run some tasks to see analytics.")
        return

    # Convert to DataFrame for analysis
    df = pd.DataFrame(st.session_state.execution_history)

    # Overview metrics
    st.markdown("### 📊 Overview Metrics")

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_tasks = len(df)
        st.metric("Total Tasks", total_tasks)

    with col2:
        avg_duration = df['execution_time'].mean()
        st.metric("Avg Duration", f"{avg_duration:.2f}s")

    with col3:
        success_count = len(df[df['status'] == 'success'])
        st.metric("Successful Tasks", success_count)

    with col4:
        if total_tasks > 0:
            success_rate = (success_count / total_tasks) * 100
            st.metric("Success Rate", f"{success_rate:.1f}%")

    # Performance trends
    st.markdown("### 📈 Performance Trends")

    # Add task numbers for plotting
    df['task_number'] = range(1, len(df) + 1)

    # Execution time trend
    fig_time = px.line(
        df,
        x='task_number',
        y='execution_time',
        title='Execution Time Trend',
        labels={'task_number': 'Task Number', 'execution_time': 'Execution Time (seconds)'}
    )
    st.plotly_chart(fig_time, use_container_width=True)

    # Status distribution
    status_counts = df['status'].value_counts()
    fig_status = px.pie(
        values=status_counts.values,
        names=status_counts.index,
        title='Task Status Distribution'
    )
    st.plotly_chart(fig_status, use_container_width=True)

    # Detailed task analysis
    st.markdown("### 📋 Detailed Task Analysis")

    # Task performance table
    display_df = df[['task', 'execution_time', 'status', 'timestamp']].copy()
    display_df['task'] = display_df['task'].str[:50] + '...'  # Truncate long tasks
    display_df['timestamp'] = pd.to_datetime(display_df['timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')

    st.dataframe(display_df, use_container_width=True)

def results_export_page():
    """Results viewing and export page."""
    st.markdown("## 💾 Results & Export")

    if not st.session_state.task_results:
        st.info("No results available yet. Execute some tasks to see results here.")
        return

    # Results browser
    st.markdown("### 📄 Results Browser")

    # Select result to view
    result_options = [f"Task {i+1}: {result.get('task', 'Unknown')[:50]}..."
                     for i, result in enumerate(st.session_state.task_results)]

    selected_idx = st.selectbox("Select Result to View", range(len(result_options)),
                               format_func=lambda x: result_options[x])

    if selected_idx is not None:
        selected_result = st.session_state.task_results[selected_idx]

        # Display result details
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown("#### Task Result")
            st.markdown(selected_result.get('result', 'No result available'))

        with col2:
            st.markdown("#### Metadata")
            st.write(f"**Agent:** {selected_result.get('agent', 'Unknown')}")
            st.write(f"**Duration:** {selected_result.get('execution_time', 0):.2f}s")
            st.write(f"**Status:** {selected_result.get('status', 'Unknown')}")
            st.write(f"**Timestamp:** {selected_result.get('timestamp', 'Unknown')}")

    # Export options
    st.markdown("### 📤 Export Options")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📄 Export as Markdown", use_container_width=True):
            export_results_as_markdown()

    with col2:
        if st.button("📊 Export as JSON", use_container_width=True):
            export_results_as_json()

    with col3:
        if st.button("📈 Export Analytics", use_container_width=True):
            export_analytics_report()

    # File management section
    st.markdown("### 📁 File Management")

    # Show saved files
    if "saved_files" in st.session_state and st.session_state.saved_files:
        st.markdown("#### Recently Saved Files")

        for i, file_info in enumerate(st.session_state.saved_files[-5:]):  # Show last 5
            with st.expander(f"📄 {file_info['task']} - {file_info['timestamp'][:10]}"):
                col1, col2 = st.columns([3, 1])

                with col1:
                    st.code(f"📄 Markdown: {file_info['filepath']}")
                    st.code(f"📊 JSON: {file_info['json_filepath']}")

                with col2:
                    # Download buttons for saved files
                    try:
                        if Path(file_info['filepath']).exists():
                            with open(file_info['filepath'], 'r', encoding='utf-8') as f:
                                content = f.read()
                            st.download_button(
                                "📥 Download MD",
                                data=content,
                                file_name=Path(file_info['filepath']).name,
                                mime="text/markdown",
                                key=f"download_md_{i}"
                            )

                        if Path(file_info['json_filepath']).exists():
                            with open(file_info['json_filepath'], 'r', encoding='utf-8') as f:
                                json_content = f.read()
                            st.download_button(
                                "📥 Download JSON",
                                data=json_content,
                                file_name=Path(file_info['json_filepath']).name,
                                mime="application/json",
                                key=f"download_json_{i}"
                            )
                    except Exception as e:
                        st.error(f"Error accessing file: {e}")

        # Clear saved files history
        if st.button("🗑️ Clear File History"):
            st.session_state.saved_files = []
            st.success("File history cleared!")
            st.rerun()
    else:
        st.info("No saved files yet. Execute tasks with auto-save enabled to see files here.")

    # Directory browser
    st.markdown("#### 📂 Output Directory Browser")

    output_dir = Path("output/results")
    if output_dir.exists():
        # Get all result files
        md_files = list(output_dir.rglob("*.md"))
        json_files = list(output_dir.rglob("*.json"))

        if md_files or json_files:
            col1, col2 = st.columns(2)

            with col1:
                st.metric("Markdown Files", len(md_files))
                if md_files:
                    latest_md = max(md_files, key=lambda x: x.stat().st_mtime)
                    st.caption(f"Latest: {latest_md.name}")

            with col2:
                st.metric("JSON Files", len(json_files))
                if json_files:
                    latest_json = max(json_files, key=lambda x: x.stat().st_mtime)
                    st.caption(f"Latest: {latest_json.name}")

            # Show directory structure
            if st.button("📁 Show Directory Structure"):
                st.code(f"output/results/\n" + "\n".join([f"  {f.relative_to(output_dir)}" for f in sorted(md_files + json_files)]))
        else:
            st.info("No files found in output directory")
    else:
        st.info("Output directory not created yet")

def export_results_as_markdown():
    """Export all results as a markdown report."""
    try:
        content = f"# OSINT Framework Results Report\n\n"
        content += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        for i, result in enumerate(st.session_state.task_results):
            content += f"## Task {i+1}\n\n"
            content += f"**Task:** {result.get('task', 'Unknown')}\n\n"
            content += f"**Agent:** {result.get('agent', 'Unknown')}\n\n"
            content += f"**Duration:** {result.get('execution_time', 0):.2f} seconds\n\n"
            content += f"**Result:**\n{result.get('result', 'No result available')}\n\n"
            content += "---\n\n"

        st.download_button(
            label="📥 Download Markdown Report",
            data=content,
            file_name=f"osint_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            mime="text/markdown"
        )

    except Exception as e:
        st.error(f"Export failed: {str(e)}")

def export_results_as_json():
    """Export all results as JSON."""
    try:
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "total_results": len(st.session_state.task_results),
            "results": st.session_state.task_results,
            "execution_history": st.session_state.execution_history
        }

        json_content = json.dumps(export_data, indent=2, default=str)

        st.download_button(
            label="📥 Download JSON Data",
            data=json_content,
            file_name=f"osint_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )

    except Exception as e:
        st.error(f"Export failed: {str(e)}")

def export_analytics_report():
    """Export analytics as a comprehensive report."""
    try:
        if not st.session_state.execution_history:
            st.warning("No execution data to export")
            return

        df = pd.DataFrame(st.session_state.execution_history)

        # Generate analytics content
        content = f"# OSINT Framework Analytics Report\n\n"
        content += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"

        # Summary statistics
        content += "## Summary Statistics\n\n"
        content += f"- Total Tasks: {len(df)}\n"
        content += f"- Successful Tasks: {len(df[df['status'] == 'success'])}\n"
        content += f"- Average Duration: {df['execution_time'].mean():.2f} seconds\n"
        content += f"- Total Execution Time: {df['execution_time'].sum():.2f} seconds\n\n"

        # Performance metrics
        content += "## Performance Metrics\n\n"
        content += f"- Fastest Task: {df['execution_time'].min():.2f} seconds\n"
        content += f"- Slowest Task: {df['execution_time'].max():.2f} seconds\n"
        content += f"- Success Rate: {(len(df[df['status'] == 'success']) / len(df) * 100):.1f}%\n\n"

        st.download_button(
            label="📥 Download Analytics Report",
            data=content,
            file_name=f"osint_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            mime="text/markdown"
        )

    except Exception as e:
        st.error(f"Export failed: {str(e)}")

def main():
    """Main application function."""
    initialize_session_state()
    display_header()
    
    # Get current page from sidebar
    if "current_page" not in st.session_state:
        st.session_state.current_page = sidebar_navigation()
    else:
        page = sidebar_navigation()
        if page != st.session_state.current_page:
            st.session_state.current_page = page
    
    # Route to appropriate page
    if st.session_state.current_page == "🏠 Agent Dashboard":
        agent_dashboard_page()
    elif st.session_state.current_page == "🔧 Agent Configuration":
        agent_configuration_page()
    elif st.session_state.current_page == "📋 Task Execution":
        task_execution_page()
    elif st.session_state.current_page == "📊 Process Monitor":
        process_monitor_page()
    elif st.session_state.current_page == "📈 Analytics":
        analytics_page()
    elif st.session_state.current_page == "💾 Results & Export":
        results_export_page()

if __name__ == "__main__":
    main()
