#!/usr/bin/env python3
"""
🧠 Enhanced OSINT Framework Demo

Quick demo script to test the enhanced Streamlit UI with all its features.
This script demonstrates the key capabilities of the enhanced interface.
"""

import os
import sys
from pathlib import Path

def setup_demo_environment():
    """Setup demo environment with sample data."""
    print("🎯 Setting up Enhanced OSINT Framework Demo")
    print("=" * 50)
    
    # Check if we're in the right directory
    project_root = Path(__file__).parent
    
    # Check for required files
    enhanced_ui_path = project_root / "ui" / "enhanced_streamlit_app.py"
    if not enhanced_ui_path.exists():
        print("❌ Enhanced UI not found. Please ensure enhanced_streamlit_app.py exists in ui/")
        return False
    
    # Check for .env file
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  Creating sample .env file...")
        sample_env = """# OSINT Framework Configuration
# Replace with your actual API keys

OPENAI_API_KEY=sk-your-openai-api-key-here
SERPER_API_KEY=your-serper-api-key-here

# Optional: Additional configuration
FRAMEWORK_DEBUG=true
LOG_LEVEL=INFO
"""
        with open(env_file, 'w') as f:
            f.write(sample_env)
        print(f"✅ Sample .env file created at {env_file}")
        print("   Please edit it with your actual API keys before running the demo.")
    
    # Create output directories
    output_dir = project_root / "output"
    output_dir.mkdir(exist_ok=True)
    
    results_dir = output_dir / "results"
    results_dir.mkdir(exist_ok=True)
    
    config_dir = project_root / "config"
    config_dir.mkdir(exist_ok=True)
    
    print("✅ Demo environment setup complete!")
    return True

def print_demo_instructions():
    """Print instructions for using the enhanced UI."""
    print("\n🎯 Enhanced OSINT Framework Demo Instructions")
    print("=" * 50)
    
    print("\n🚀 Getting Started:")
    print("1. Ensure your API keys are configured in the .env file")
    print("2. Run: python run_ui.py")
    print("3. Open your browser to http://localhost:8501")
    
    print("\n🎨 Enhanced Features to Try:")
    print("\n📱 Agent Dashboard:")
    print("   - View agent overview and quick stats")
    print("   - Access quick action buttons")
    print("   - Review recent activity")
    
    print("\n🔧 Agent Configuration:")
    print("   - Select different agent types (CTI, Geopolitical, Browser OSINT, Multimodal)")
    print("   - Customize agent role, goal, and backstory")
    print("   - Configure tool settings (enable/disable features)")
    print("   - Adjust LLM parameters (model, temperature, max tokens)")
    print("   - Save and load custom configurations")
    
    print("\n📋 Task Execution:")
    print("   - Use pre-built task templates for each agent type")
    print("   - Input custom tasks with additional context")
    print("   - Enable real-time monitoring during execution")
    print("   - Auto-save results to files")
    
    print("\n📊 Process Monitor:")
    print("   - Watch real-time agent process steps")
    print("   - View execution timeline and performance metrics")
    print("   - Monitor tool usage and agent status")
    print("   - Track success rates and execution times")
    
    print("\n📈 Analytics:")
    print("   - View performance trends over time")
    print("   - Analyze task success rates")
    print("   - Review execution time statistics")
    print("   - Export analytics reports")
    
    print("\n💾 Results & Export:")
    print("   - Browse all task results")
    print("   - Export results as Markdown or JSON")
    print("   - Download comprehensive analytics reports")
    print("   - Save individual results to files")

def print_sample_tasks():
    """Print sample tasks for each agent type."""
    print("\n📝 Sample Tasks to Try:")
    print("=" * 30)
    
    print("\n🔒 CTI Agent Tasks:")
    print("   - 'Analyze this malware hash: d41d8cd98f00b204e9800998ecf8427e'")
    print("   - 'Track the threat actor APT28 and provide a profile'")
    print("   - 'Extract IOCs from this threat report: [paste report text]'")
    
    print("\n🌍 Geopolitical Agent Tasks:")
    print("   - 'Provide an intelligence brief on current Middle East tensions'")
    print("   - 'Analyze recent developments in Eastern Europe'")
    print("   - 'Generate a situation report on Asia-Pacific security'")
    
    print("\n🌐 Browser OSINT Agent Tasks:")
    print("   - 'Investigate the domain example.com for suspicious activities'")
    print("   - 'Collect OSINT on the organization [company name]'")
    print("   - 'Analyze the website structure of [URL]'")
    
    print("\n📸 Multimodal OSINT Agent Tasks:")
    print("   - 'Analyze this image for identifying information'")
    print("   - 'Extract text from this document using OCR'")
    print("   - 'Identify objects and faces in this image'")

def check_system_requirements():
    """Check if system meets requirements for the demo."""
    print("\n🔍 Checking System Requirements...")
    
    required_packages = [
        'streamlit',
        'crewai',
        'langchain',
        'llama_index',
        'plotly',
        'pandas'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Missing packages: {', '.join(missing)}")
        print("Install with: pip install " + " ".join(missing))
        return False
    
    print("✅ All required packages are installed!")
    return True

def main():
    """Main demo function."""
    print("🧠 Enhanced OSINT Framework Demo")
    print("=" * 40)
    
    # Setup demo environment
    if not setup_demo_environment():
        sys.exit(1)
    
    # Check system requirements
    if not check_system_requirements():
        print("\nPlease install missing packages and try again.")
        sys.exit(1)
    
    # Print instructions
    print_demo_instructions()
    print_sample_tasks()
    
    print("\n" + "=" * 50)
    print("🎉 Demo setup complete!")
    print("\nTo start the enhanced UI, run:")
    print("   python run_ui.py")
    print("\nOr to skip dependency checks:")
    print("   python run_ui.py --skip-checks")
    print("\nTo use the original UI instead:")
    print("   python run_ui.py --original-ui")
    print("=" * 50)

if __name__ == "__main__":
    main()
