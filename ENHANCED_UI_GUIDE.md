# 🧠 Enhanced OSINT Framework UI Guide

Welcome to the Enhanced OSINT Framework UI! This comprehensive interface makes it easy to work with AI agents for intelligence analysis with advanced customization and real-time monitoring capabilities.

## 🚀 Quick Start

### 1. Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Configure API keys in .env file
OPENAI_API_KEY=your_openai_key_here
SERPER_API_KEY=your_serper_key_here

# Run the enhanced UI
python run_ui.py
```

### 2. Access the Interface
Open your browser to: http://localhost:8501

## 🎯 Key Features

### 🏠 Agent Dashboard
- **Overview**: Get a quick overview of your selected agent and its capabilities
- **Quick Stats**: View metrics like tasks completed, tools available, and average execution time
- **Quick Actions**: Direct access to configuration, task execution, and monitoring
- **Recent Activity**: Review your last few task executions

### 🔧 Agent Configuration
The configuration page has four main tabs:

#### 🤖 Agent Settings
- **Agent Selection**: Choose from CTI, Geopolitical, Browser OSINT, or Multimodal agents
- **Role Customization**: Define the agent's role and expertise area
- **Goal Setting**: Specify what the agent should accomplish
- **Backstory**: Provide context and background for better performance
- **Reset to Defaults**: Quickly restore default settings

#### 🛠️ Tool Configuration
- **Enable/Disable Tools**: Toggle web search, web crawling, and RAG capabilities
- **Search Settings**: Configure timeout and maximum results
- **Tool Status**: Visual indicators showing which tools are active
- **Performance Tuning**: Adjust tool parameters for optimal performance

#### 🧠 LLM Settings
- **Model Selection**: Choose between GPT-4, GPT-4-turbo, or GPT-3.5-turbo
- **Temperature**: Control response randomness (0.0 = deterministic, 1.0 = creative)
- **Max Tokens**: Set maximum response length
- **Verbose Logging**: Enable detailed output for debugging

#### 💾 Save/Load Configurations
- **Save Custom Configs**: Store your agent configurations for reuse
- **Load Saved Configs**: Quickly apply previously saved settings
- **Configuration Management**: Organize and manage multiple agent setups

### 📋 Task Execution
- **Task Templates**: Pre-built templates for each agent type
- **Custom Tasks**: Input your own analysis requests
- **Additional Context**: Provide extra information to guide the agent
- **Execution Options**: Enable monitoring, auto-save, and detailed logging
- **Real-time Feedback**: Watch your task execute with live updates

### 📊 Process Monitor - **KEY FEATURE**
This is where you can follow the agent's work in real-time:

#### Real-time Process Tracking
- **Step-by-Step Monitoring**: See each phase of agent execution
- **Live Status Updates**: Watch as the agent initializes, prepares tools, and executes tasks
- **Progress Indicators**: Visual progress bars and status indicators
- **Detailed Logging**: Comprehensive logs of all agent activities

#### Process Visualization
- **Timeline View**: See execution steps in chronological order
- **Status Colors**: 
  - 🔄 Active (currently running)
  - ✅ Completed (successfully finished)
  - ❌ Error (failed step)
- **Execution Metrics**: Duration, success rates, and performance data

#### Agent Performance Insights
- **Tool Usage**: Monitor which tools the agent is using
- **Knowledge Base Status**: Check if RAG is available and active
- **Model Information**: See which LLM model is being used
- **Capability Overview**: Review agent's available functions

### 📈 Analytics
- **Performance Trends**: Track execution times and success rates over time
- **Task Distribution**: See what types of tasks you're running most
- **Success Metrics**: Monitor your agent's performance statistics
- **Historical Data**: Review past executions and identify patterns

### 💾 Results & Export
- **Results Browser**: View and navigate through all task results
- **Rich Formatting**: Results displayed with proper markdown rendering
- **Export Options**:
  - 📄 Markdown reports
  - 📊 JSON data files
  - 📈 Analytics reports
- **File Management**: Auto-save results and organize outputs

## 🎨 Agent Types & Capabilities

### 🔒 CTI Agent (Cyber Threat Intelligence)
- **IOC Extraction**: Extract indicators of compromise from threat reports
- **Threat Actor Tracking**: Research and profile threat actors
- **Campaign Analysis**: Correlate threats and analyze attack campaigns
- **Intelligence Reports**: Generate comprehensive CTI reports

### 🌍 Geopolitical Agent
- **Intelligence Briefings**: Create geopolitical intelligence summaries
- **Regional Monitoring**: Track developments in specific regions
- **Situation Reports**: Generate multi-region situation assessments
- **Trend Analysis**: Analyze geopolitical trends and implications

### 🌐 Browser OSINT Agent
- **Domain Investigation**: Research websites and domains
- **Social Media Analysis**: Collect intelligence from social platforms
- **Organization Research**: Gather open source intelligence on entities
- **Web Technology Analysis**: Analyze website structures and technologies

### 📸 Multimodal OSINT Agent
- **Image Analysis**: Extract information from images and photos
- **OCR Processing**: Extract text from documents and images
- **Face Recognition**: Identify faces in images and videos
- **Metadata Extraction**: Analyze file metadata and EXIF data

## 🔧 Advanced Usage

### Custom Agent Configurations
1. Go to **Agent Configuration** tab
2. Customize the agent's role, goal, and backstory
3. Configure tools and LLM settings
4. Save your configuration with a memorable name
5. Load it anytime for consistent results

### Real-time Monitoring
1. Enable monitoring in **Task Execution**
2. Switch to **Process Monitor** tab during execution
3. Watch real-time progress and logs
4. Use auto-refresh for continuous updates

### Batch Processing
1. Save successful task configurations
2. Load them for similar tasks
3. Use templates for common analysis types
4. Export results for further processing

## 🛠️ Troubleshooting

### Common Issues
- **API Key Errors**: Ensure your .env file has valid API keys
- **Import Errors**: Install missing dependencies with `pip install -r requirements.txt`
- **Agent Initialization**: Check that all required tools are available
- **Performance Issues**: Adjust LLM settings and tool timeouts

### Getting Help
- Check the process monitor for detailed error information
- Review the analytics page for performance insights
- Use verbose logging for debugging
- Ensure all dependencies are properly installed

## 🎯 Best Practices

1. **Start Simple**: Begin with default configurations and templates
2. **Monitor Performance**: Use the process monitor to understand agent behavior
3. **Save Configurations**: Store successful setups for reuse
4. **Export Results**: Regularly export your analysis results
5. **Review Analytics**: Use insights to optimize your workflows

## 🔄 Updates and Maintenance

The enhanced UI automatically saves your session data and configurations. For best performance:
- Regularly clear old logs and results
- Update API keys as needed
- Monitor system resource usage
- Keep dependencies up to date

---

**Happy Intelligence Gathering! 🕵️‍♂️**

For more information, see the main project documentation or contact the development team.
