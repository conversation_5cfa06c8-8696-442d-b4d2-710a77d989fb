#!/usr/bin/env python3
"""
🧠 OSINT Framework - Complete Startup Script

This script provides a complete initialization and startup sequence for the
Enhanced OSINT Framework with comprehensive setup, validation, and launch.
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from typing import Dict, Any, List, Tuple

def print_banner():
    """Print the framework banner."""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║                    🧠 Enhanced OSINT Framework v2.0                         ║
║                                                                              ║
║              Advanced AI-Powered Intelligence Analysis Platform              ║
║                     with Real-Time Process Monitoring                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_python_version() -> bool:
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - Compatible")
    return True

def setup_directories():
    """Create necessary directories."""
    print("📁 Setting up directory structure...")
    
    directories = [
        "config",
        "config/saved_configs",
        "output",
        "output/results",
        "logs",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}/")
    
    print("✅ Directory structure created")

def check_and_install_dependencies() -> bool:
    """Check and optionally install dependencies."""
    print("📦 Checking dependencies...")
    
    required_packages = {
        'streamlit': 'streamlit>=1.28.0',
        'crewai': 'crewai',
        'langchain': 'langchain',
        'langchain_openai': 'langchain-openai',
        'llama_index': 'llama-index',
        'plotly': 'plotly>=5.17.0',
        'pandas': 'pandas>=2.0.0',
        'requests': 'requests>=2.31.0',
        'python_dotenv': 'python-dotenv>=1.0.0'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package}")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n📥 Missing packages detected: {len(missing_packages)}")
        
        install = input("Would you like to install missing packages? (y/n): ").lower().strip()
        if install == 'y':
            print("Installing missing packages...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install"
                ] + missing_packages)
                print("✅ All packages installed successfully!")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install packages: {e}")
                return False
        else:
            print("⚠️  Some features may not work without all dependencies")
            return False
    
    print("✅ All dependencies are installed")
    return True

def setup_environment_file() -> bool:
    """Setup .env file with API keys."""
    print("🔑 Checking environment configuration...")
    
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file found")
        
        # Load and validate
        from dotenv import load_dotenv
        load_dotenv()
        
        openai_key = os.getenv("OPENAI_API_KEY")
        serper_key = os.getenv("SERPER_API_KEY")
        
        if openai_key and openai_key.startswith("sk-"):
            print("   ✅ OpenAI API key configured")
        else:
            print("   ⚠️  OpenAI API key missing or invalid")
        
        if serper_key and len(serper_key) >= 32:
            print("   ✅ Serper API key configured")
        else:
            print("   ⚠️  Serper API key missing or invalid")
        
        return True
    else:
        print("⚠️  .env file not found")
        
        create_env = input("Would you like to create a .env file? (y/n): ").lower().strip()
        if create_env == 'y':
            print("\n📝 Creating .env file...")
            print("Please provide your API keys (press Enter to skip):")
            
            openai_key = input("OpenAI API Key (sk-...): ").strip()
            serper_key = input("Serper API Key: ").strip()
            
            env_content = f"""# Enhanced OSINT Framework Configuration
# Generated: {Path(__file__).name}

# Required API Keys
OPENAI_API_KEY={openai_key or 'your-openai-api-key-here'}
SERPER_API_KEY={serper_key or 'your-serper-api-key-here'}

# Optional Configuration
FRAMEWORK_DEBUG=false
LOG_LEVEL=WARNING
MAX_CONCURRENT_TASKS=3
DEFAULT_AGENT_TYPE=cti_agent
SUPPRESS_WARNINGS=true

# UI Configuration
STREAMLIT_PORT=8501
STREAMLIT_HOST=localhost
"""
            
            with open(env_file, 'w') as f:
                f.write(env_content)
            
            print(f"✅ .env file created at {env_file}")
            print("   Please edit it with your actual API keys before running the framework")
            return True
        else:
            print("⚠️  Framework will run with limited functionality")
            return False

def create_default_config():
    """Create default configuration files."""
    print("⚙️  Creating default configuration...")
    
    config_file = Path("config/framework_config.json")
    
    if not config_file.exists():
        default_config = {
            "version": "2.0",
            "ui_settings": {
                "theme": "light",
                "auto_save": True,
                "max_file_size_mb": 10,
                "default_agent": "cti_agent",
                "show_advanced_options": True,
                "enable_real_time_monitoring": True
            },
            "agent_defaults": {
                "llm_model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 2000,
                "verbose": True
            },
            "tool_settings": {
                "enable_web_search": True,
                "enable_web_crawling": True,
                "enable_rag": True,
                "search_timeout": 30,
                "max_search_results": 10
            },
            "monitoring": {
                "enable_process_tracking": True,
                "log_level": "INFO",
                "save_execution_logs": True,
                "max_log_entries": 1000
            }
        }
        
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=2)
        
        print(f"✅ Default configuration created at {config_file}")
    else:
        print("✅ Configuration file already exists")

def validate_framework():
    """Validate framework components."""
    print("🔍 Validating framework components...")
    
    # Check critical files
    critical_files = [
        "ui/enhanced_streamlit_app.py",
        "agents/base_agent.py",
        "agents/cti_agent.py",
        "agents/geo_agent.py"
    ]
    
    for file_path in critical_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - Missing critical file!")
            return False
    
    print("✅ Framework validation complete")
    return True

def launch_framework():
    """Launch the enhanced UI."""
    print("🚀 Launching Enhanced OSINT Framework...")
    
    try:
        # Use the enhanced launcher
        subprocess.run([sys.executable, "run_ui.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to launch framework: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 Framework shutdown requested")
        return True
    
    return True

def main():
    """Main startup function."""
    print_banner()
    
    print("🔧 Starting Enhanced OSINT Framework initialization...")
    print("=" * 80)
    
    # Step 1: Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Setup directories
    setup_directories()
    
    # Step 3: Check dependencies
    if not check_and_install_dependencies():
        print("\n⚠️  Continuing with limited functionality...")
    
    # Step 4: Setup environment
    setup_environment_file()
    
    # Step 5: Create default config
    create_default_config()
    
    # Step 6: Validate framework
    if not validate_framework():
        print("❌ Framework validation failed!")
        sys.exit(1)
    
    print("\n" + "=" * 80)
    print("✅ Framework initialization complete!")
    print("\n🎯 Enhanced Features Available:")
    print("   • 🤖 Dynamic agent selection and configuration")
    print("   • 🔧 Custom system prompts and tool settings")
    print("   • 📊 Real-time task execution monitoring")
    print("   • 📝 Rich markdown output rendering")
    print("   • 💾 Comprehensive file save options")
    print("   • 📈 Advanced analytics and insights")
    print("   • 🔄 Configuration management")
    
    print("\n" + "=" * 80)
    
    # Launch framework
    launch_input = input("Launch the Enhanced UI now? (y/n): ").lower().strip()
    if launch_input == 'y':
        launch_framework()
    else:
        print("\n🎉 Setup complete! Run 'python run_ui.py' when ready to start.")

if __name__ == "__main__":
    main()
