# 🔧 Enhanced OSINT Framework - Troubleshooting Guide

## 🚀 Quick Fixes for Common Issues

### ⚠️ **Warning Messages**

#### "LangGraph not available, using fallback memory system"
**What it means:** The system is using a simpler memory system instead of the advanced LangGraph features.

**Is this a problem?** No! The framework works perfectly fine without LangGraph.

**To fix (optional):**
```bash
pip install langgraph
```

**To suppress the warning:**
Add to your `.env` file:
```
FRAMEWORK_DEBUG=false
LOG_LEVEL=WARNING
```

#### "Missing ScriptRunContext" warnings
**What it means:** Streamlit warnings when importing the UI module directly.

**Is this a problem?** No! These are harmless warnings that appear during testing.

**To fix:** These warnings only appear when importing the UI module directly. They don't appear when running the actual UI.

### 🔑 **API Key Issues**

#### "API Keys not configured"
**Solution:**
1. Create/edit `.env` file in the project root
2. Add your API keys:
```
OPENAI_API_KEY=sk-your-actual-openai-key-here
SERPER_API_KEY=your-actual-serper-key-here
```

#### "Invalid API key format"
**OpenAI keys** should start with `sk-` and be about 50+ characters
**Serper keys** should be 32+ characters of alphanumeric text

### 📦 **Dependency Issues**

#### "Module not found" errors
**Solution:**
```bash
# Install all required dependencies
pip install -r requirements.txt

# Or install core dependencies only
pip install streamlit crewai langchain llama-index plotly pandas python-dotenv
```

#### "Package not installed" warnings in IDE
**What it means:** Your IDE is showing packages that aren't installed in your current environment.

**Solution:**
- These are mostly optional packages for advanced features
- The framework will work with core packages
- Install only what you need for your use case

### 🖥️ **UI Issues**

#### "Streamlit app not found"
**Solution:**
```bash
# Make sure you're in the project directory
cd /path/to/Agent02

# Run the enhanced launcher
python run_ui.py

# Or run Streamlit directly
streamlit run ui/enhanced_streamlit_app.py
```

#### "Port already in use"
**Solution:**
```bash
# Use a different port
python run_ui.py --port 8502

# Or kill the process using the port
lsof -ti:8501 | xargs kill -9
```

#### "Agent initialization failed"
**Check:**
1. API keys are properly configured
2. Internet connection is working
3. No firewall blocking requests

### 🤖 **Agent Issues**

#### "Failed to initialize agent"
**Common causes:**
1. Missing API keys
2. Invalid API keys
3. Network connectivity issues
4. Rate limiting

**Solution:**
1. Verify API keys in `.env` file
2. Test API keys with a simple request
3. Check internet connection
4. Wait a few minutes if rate limited

#### "No tools available"
**What it means:** Agent couldn't initialize any tools.

**Solution:**
1. Check API keys
2. Verify network connectivity
3. Check if required packages are installed

### 📊 **Process Monitor Issues**

#### "No process activity to display"
**What it means:** No tasks have been executed yet.

**Solution:**
1. Go to Task Execution tab
2. Configure an agent first
3. Run a task with monitoring enabled
4. Switch to Process Monitor tab during execution

#### "Real-time updates not working"
**Solution:**
1. Enable auto-refresh in Process Monitor
2. Refresh the page manually
3. Check browser console for errors

### 💾 **File Save Issues**

#### "Failed to save results"
**Common causes:**
1. Insufficient disk space
2. Permission issues
3. Invalid file paths

**Solution:**
1. Check available disk space
2. Ensure write permissions to output directory
3. Try running with administrator/sudo privileges

#### "Output directory not found"
**Solution:**
The framework should create directories automatically. If not:
```bash
mkdir -p output/results
mkdir -p config/saved_configs
```

### 🔧 **Configuration Issues**

#### "Configuration not loading"
**Solution:**
1. Check file permissions
2. Verify JSON syntax in config files
3. Delete corrupted config files and recreate

#### "Settings not persisting"
**Solution:**
1. Ensure write permissions to config directory
2. Check available disk space
3. Verify config files aren't read-only

## 🆘 **Getting Help**

### **Step 1: Check the Logs**
Look for error messages in:
- Terminal output
- Browser console (F12)
- Process Monitor tab

### **Step 2: Verify Environment**
```bash
# Check Python version (3.8+ required)
python --version

# Check if in correct directory
ls -la | grep enhanced_streamlit_app.py

# Check environment variables
echo $OPENAI_API_KEY
```

### **Step 3: Test Basic Functionality**
```bash
# Test imports
python -c "import streamlit; print('Streamlit OK')"
python -c "import crewai; print('CrewAI OK')"

# Test UI import
python -c "import ui.enhanced_streamlit_app; print('UI OK')"
```

### **Step 4: Reset to Defaults**
If all else fails:
```bash
# Backup your .env file
cp .env .env.backup

# Run the setup script
python start_osint_framework.py

# Restore your API keys
cp .env.backup .env
```

## 🎯 **Performance Tips**

### **Faster Startup**
- Set `LOG_LEVEL=WARNING` in `.env`
- Use `--skip-checks` flag: `python run_ui.py --skip-checks`

### **Better Performance**
- Close unused browser tabs
- Reduce `max_search_results` in tool configuration
- Lower `max_tokens` for faster responses

### **Memory Usage**
- Clear process logs regularly
- Limit analysis history
- Use smaller LLM models for testing

## ✅ **Verification Checklist**

Before reporting issues, verify:
- [ ] Python 3.8+ installed
- [ ] In correct project directory
- [ ] API keys configured in `.env`
- [ ] Core dependencies installed
- [ ] Internet connection working
- [ ] No firewall blocking requests
- [ ] Sufficient disk space
- [ ] Write permissions to project directory

## 🎉 **Everything Working?**

If the framework is working correctly, you should see:
- ✅ Enhanced UI loads without errors
- ✅ Agents can be configured and initialized
- ✅ Tasks execute successfully
- ✅ Real-time monitoring shows process steps
- ✅ Results are saved and exportable

**Happy Intelligence Gathering! 🕵️‍♂️**
