#!/usr/bin/env python3
"""
🧠 CrewAI OSINT Agent Framework - Enhanced UI Launcher

Comprehensive launcher script that initializes the entire framework and provides
an easy-to-use interface for agent management, task execution, and customization.
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, Any, Optional
import argparse

def setup_environment():
    """Setup the environment and validate dependencies."""
    project_root = Path(__file__).parent

    # Add project root to Python path
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))

    # Load environment variables
    env_file = project_root / ".env"
    if env_file.exists():
        from dotenv import load_dotenv
        load_dotenv(env_file)
        print("✅ Environment variables loaded from .env")
    else:
        print("⚠️  Warning: .env file not found.")
        print("Please create a .env file with your API keys:")
        print("OPENAI_API_KEY=your_openai_key")
        print("SERPER_API_KEY=your_serper_key")
        print()

    return project_root

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        'streamlit',
        'crewai',
        'langchain',
        'llama_index',
        'dspy',
        'plotly',
        'pandas'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("✅ All required dependencies are installed")
    return True

def validate_api_keys():
    """Validate API key configuration."""
    openai_key = os.getenv("OPENAI_API_KEY")
    serper_key = os.getenv("SERPER_API_KEY")

    issues = []

    if not openai_key or not openai_key.startswith("sk-"):
        issues.append("OPENAI_API_KEY is missing or invalid")

    if not serper_key or len(serper_key) < 32:
        issues.append("SERPER_API_KEY is missing or invalid")

    if issues:
        print("⚠️  API Key Issues:")
        for issue in issues:
            print(f"   - {issue}")
        print("\nThe framework will run with limited functionality.")
        return False

    print("✅ API keys are properly configured")
    return True

def create_default_config():
    """Create default configuration files if they don't exist."""
    project_root = Path(__file__).parent
    config_dir = project_root / "config"
    config_dir.mkdir(exist_ok=True)

    # Default agent configurations
    default_configs = {
        "ui_settings": {
            "theme": "light",
            "auto_save": True,
            "max_file_size_mb": 10,
            "default_agent": "cti_agent",
            "show_advanced_options": False
        },
        "agent_defaults": {
            "llm_model": "gpt-4",
            "temperature": 0.1,
            "max_tokens": 2000,
            "verbose": True
        },
        "tool_settings": {
            "enable_web_search": True,
            "enable_web_crawling": True,
            "enable_rag": True,
            "search_timeout": 30,
            "max_search_results": 10
        }
    }

    config_file = config_dir / "ui_config.json"
    if not config_file.exists():
        with open(config_file, 'w') as f:
            json.dump(default_configs, f, indent=2)
        print(f"✅ Created default configuration at {config_file}")

    return config_file

def launch_streamlit_ui(project_root: Path, port: int = 8501):
    """Launch the enhanced Streamlit UI."""
    ui_app_path = project_root / "ui" / "enhanced_streamlit_app.py"

    # If enhanced app doesn't exist, fall back to original
    if not ui_app_path.exists():
        ui_app_path = project_root / "ui" / "streamlit_app.py"
        if not ui_app_path.exists():
            print("❌ Error: No Streamlit app found")
            return False

    print("🚀 Starting Enhanced OSINT Framework UI...")
    print(f"📁 Project root: {project_root}")
    print(f"🌐 UI will be available at: http://localhost:{port}")
    print("🎯 Features available:")
    print("   - Dynamic agent selection and configuration")
    print("   - Custom system prompts and tool settings")
    print("   - Real-time task execution monitoring")
    print("   - Rich markdown output rendering")
    print("   - Comprehensive file save options")
    print()

    try:
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            str(ui_app_path),
            "--server.port", str(port),
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false",
            "--theme.base", "light"
        ]

        subprocess.run(cmd, cwd=str(project_root))
        return True

    except KeyboardInterrupt:
        print("\n👋 Shutting down OSINT Framework UI...")
        return True
    except Exception as e:
        print(f"❌ Error launching UI: {e}")
        return False

def main():
    """Main launcher function with comprehensive initialization."""
    parser = argparse.ArgumentParser(description="OSINT Framework Enhanced UI Launcher")
    parser.add_argument("--port", type=int, default=8501, help="Port for Streamlit UI")
    parser.add_argument("--skip-checks", action="store_true", help="Skip dependency and API key checks")
    parser.add_argument("--create-config", action="store_true", help="Create default configuration files")

    args = parser.parse_args()

    print("🧠 CrewAI OSINT Agent Framework - Enhanced Launcher")
    print("=" * 60)

    # Setup environment
    project_root = setup_environment()

    # Create default configuration if requested
    if args.create_config:
        create_default_config()
        print("Configuration files created. You can now run the UI.")
        return

    # Perform checks unless skipped
    if not args.skip_checks:
        print("\n🔍 Performing system checks...")

        if not check_dependencies():
            sys.exit(1)

        validate_api_keys()  # Warning only, don't exit

    # Create default config if it doesn't exist
    create_default_config()

    # Launch UI
    print("\n🚀 Launching UI...")
    success = launch_streamlit_ui(project_root, args.port)

    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
